{"name": "google-workspace-mcp", "version": "1.0.0", "description": "Production-ready MCP server for Google Workspace with full feature coverage", "author": "Google Workspace MCP Team", "license": "MIT", "homepage": "https://github.com/your-org/google-workspace-mcp", "repository": "https://github.com/your-org/google-workspace-mcp.git", "keywords": ["mcp", "google-workspace", "gmail", "drive", "docs", "sheets", "slides", "calendar", "forms", "contacts", "tasks", "keep"], "mcp": {"version": "1.0.0", "capabilities": ["tools", "resources", "prompts", "logging", "sampling"], "authentication": {"methods": ["oauth2", "service_account"], "scopes": ["https://mail.google.com/", "https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/documents", "https://www.googleapis.com/auth/spreadsheets", "https://www.googleapis.com/auth/presentations", "https://www.googleapis.com/auth/calendar", "https://www.googleapis.com/auth/contacts", "https://www.googleapis.com/auth/tasks", "https://www.googleapis.com/auth/keep", "https://www.googleapis.com/auth/forms.body", "https://www.googleapis.com/auth/forms.responses.readonly", "https://www.googleapis.com/auth/gmail.readonly", "https://www.googleapis.com/auth/drive.readonly", "https://www.googleapis.com/auth/documents.readonly", "https://www.googleapis.com/auth/spreadsheets.readonly", "https://www.googleapis.com/auth/presentations.readonly", "https://www.googleapis.com/auth/calendar.readonly", "https://www.googleapis.com/auth/contacts.readonly", "https://www.googleapis.com/auth/tasks.readonly", "https://www.googleapis.com/auth/keep.readonly"]}, "rateLimit": {"enabled": true, "windowMs": 60000, "maxRequests": 100}, "security": {"rbac": true, "encryption": true, "audit": true}}, "tools": {"gmail": {"version": "1.0.0", "description": "Gmail email management tools", "tools": {"send_email": {"description": "Send an email through Gmail with optional attachments and threading support", "inputSchema": {"type": "object", "properties": {"to": {"type": "array", "items": {"type": "string"}, "description": "Array of recipient email addresses"}, "subject": {"type": "string", "description": "Email subject line"}, "html": {"type": "string", "description": "HTML content of the email"}, "text": {"type": "string", "description": "Plain text content of the email"}, "cc": {"type": "array", "items": {"type": "string"}, "description": "Array of CC recipient email addresses"}, "bcc": {"type": "array", "items": {"type": "string"}, "description": "Array of BCC recipient email addresses"}, "attachments": {"type": "array", "items": {"type": "object", "properties": {"filename": {"type": "string"}, "content": {"type": "string"}, "mimeType": {"type": "string"}, "size": {"type": "number"}}}, "description": "Array of email attachments"}, "threadId": {"type": "string", "description": "Thread ID to reply to an existing conversation"}}, "required": ["to", "subject"]}, "requiredScopes": ["https://mail.google.com/"], "idempotent": false, "safeMode": false, "deprecated": false}, "search": {"description": "Search Gmail messages using Gmail search syntax", "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "Gmail search query (supports Gmail search operators)"}, "maxResults": {"type": "number", "default": 10, "description": "Maximum number of results to return"}, "pageToken": {"type": "string", "description": "Token for pagination to get next page of results"}}, "required": ["query"]}, "requiredScopes": ["https://www.googleapis.com/auth/gmail.readonly"], "idempotent": true, "safeMode": true, "deprecated": false}, "get_message": {"description": "Retrieve a specific Gmail message by ID with configurable detail level", "inputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Gmail message ID"}, "format": {"type": "string", "enum": ["minimal", "full", "raw", "metadata"], "default": "full", "description": "Level of detail to retrieve"}}, "required": ["id"]}, "requiredScopes": ["https://www.googleapis.com/auth/gmail.readonly"], "idempotent": true, "safeMode": true, "deprecated": false}, "modify_labels": {"description": "Add or remove labels from a Gmail message", "inputSchema": {"type": "object", "properties": {"messageId": {"type": "string", "description": "Gmail message ID to modify"}, "addLabelIds": {"type": "array", "items": {"type": "string"}, "description": "Array of label IDs to add to the message"}, "removeLabelIds": {"type": "array", "items": {"type": "string"}, "description": "Array of label IDs to remove from the message"}}, "required": ["messageId"]}, "requiredScopes": ["https://mail.google.com/"], "idempotent": false, "safeMode": false, "deprecated": false}, "list_threads": {"description": "List Gmail conversation threads with optional search filtering", "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "Gmail search query to filter threads"}, "maxResults": {"type": "number", "default": 10, "description": "Maximum number of threads to return"}, "pageToken": {"type": "string", "description": "Token for pagination to get next page of results"}}}, "requiredScopes": ["https://www.googleapis.com/auth/gmail.readonly"], "idempotent": true, "safeMode": true, "deprecated": false}, "watch_start": {"description": "Start watching for Gmail message changes via push notifications", "inputSchema": {"type": "object", "properties": {"topicName": {"type": "string", "description": "Google Cloud Pub/Sub topic name for notifications"}, "labelIds": {"type": "array", "items": {"type": "string"}, "description": "Array of label IDs to watch for changes"}, "labelFilterAction": {"type": "string", "enum": ["include", "exclude"], "description": "Whether to include or exclude the specified labels"}}, "required": ["topicName"]}, "requiredScopes": ["https://mail.google.com/"], "idempotent": false, "safeMode": false, "deprecated": false}, "watch_stop": {"description": "Stop watching for Gmail message changes", "inputSchema": {"type": "object", "properties": {}}, "requiredScopes": ["https://mail.google.com/"], "idempotent": true, "safeMode": true, "deprecated": false}}}}}