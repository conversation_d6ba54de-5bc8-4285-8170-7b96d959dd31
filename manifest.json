{"name": "google-workspace-mcp", "version": "1.0.0", "description": "Production-ready MCP server for Google Workspace with full feature coverage", "author": "Google Workspace MCP Team", "license": "MIT", "homepage": "https://github.com/your-org/google-workspace-mcp", "repository": "https://github.com/your-org/google-workspace-mcp.git", "keywords": ["mcp", "google-workspace", "gmail", "drive", "docs", "sheets", "slides", "calendar", "forms", "contacts", "tasks", "keep"], "mcp": {"version": "1.0.0", "capabilities": ["tools", "resources", "prompts", "logging", "sampling"], "authentication": {"methods": ["oauth2", "service_account"], "scopes": ["https://mail.google.com/", "https://www.googleapis.com/auth/gmail.readonly"]}, "rateLimit": {"enabled": true, "windowMs": 60000, "maxRequests": 100}, "security": {"rbac": true, "encryption": true, "audit": true}}, "tools": {"gmail": {"version": "1.0.0", "description": "Gmail email management tools", "tools": {"send_email": {"description": "Send an email through Gmail with optional attachments and threading support", "inputSchema": {"type": "object"}, "requiredScopes": ["https://mail.google.com/"], "idempotent": false, "safeMode": false, "deprecated": false, "examples": [{"name": "Send simple email", "description": "Send a basic email with text content", "parameters": {"to": ["<EMAIL>"], "subject": "Hello from MCP", "text": "This is a test email sent through the MCP Gmail integration."}}, {"name": "Send HTML email with CC", "description": "Send an HTML email with CC recipients", "parameters": {"to": ["<EMAIL>"], "cc": ["<EMAIL>"], "subject": "Project Update", "html": "<h1>Project Status</h1><p>The project is <strong>on track</strong>.</p>"}}]}, "search": {"description": "Search Gmail messages using Gmail search syntax", "inputSchema": {"type": "object", "properties": {"query": {"type": "string"}, "maxResults": {"type": "object"}, "pageToken": {"type": "object"}}, "required": ["query"]}, "requiredScopes": ["https://www.googleapis.com/auth/gmail.readonly"], "idempotent": true, "safeMode": true, "deprecated": false, "examples": [{"name": "Search by sender", "description": "Find emails from a specific sender", "parameters": {"query": "from:<EMAIL>", "maxResults": 20}}, {"name": "Search unread emails", "description": "Find all unread emails", "parameters": {"query": "is:unread", "maxResults": 50}}]}, "get_message": {"description": "Retrieve a specific Gmail message by ID with configurable detail level", "inputSchema": {"type": "object", "properties": {"id": {"type": "string"}, "format": {"type": "object"}}, "required": ["id"]}, "requiredScopes": ["https://www.googleapis.com/auth/gmail.readonly"], "idempotent": true, "safeMode": true, "deprecated": false, "examples": [{"name": "Get full message", "description": "Retrieve complete message details", "parameters": {"id": "18b2e1a2f3c4d5e6", "format": "full"}}, {"name": "Get message metadata only", "description": "Retrieve just the message headers and metadata", "parameters": {"id": "18b2e1a2f3c4d5e6", "format": "metadata"}}]}, "modify_labels": {"description": "Add or remove labels from a Gmail message", "inputSchema": {"type": "object"}, "requiredScopes": ["https://mail.google.com/"], "idempotent": false, "safeMode": false, "deprecated": false, "examples": [{"name": "Mark as important", "description": "Add the important label to a message", "parameters": {"messageId": "18b2e1a2f3c4d5e6", "addLabelIds": ["IMPORTANT"]}}, {"name": "Archive message", "description": "Remove inbox label to archive a message", "parameters": {"messageId": "18b2e1a2f3c4d5e6", "removeLabelIds": ["INBOX"]}}]}, "list_threads": {"description": "List Gmail conversation threads with optional search filtering", "inputSchema": {"type": "object", "properties": {"query": {"type": "object"}, "maxResults": {"type": "object"}, "pageToken": {"type": "object"}}, "required": []}, "requiredScopes": ["https://www.googleapis.com/auth/gmail.readonly"], "idempotent": true, "safeMode": true, "deprecated": false, "examples": [{"name": "List recent threads", "description": "Get the most recent conversation threads", "parameters": {"maxResults": 25}}, {"name": "List threads from specific sender", "description": "Get conversation threads from a specific sender", "parameters": {"query": "from:<EMAIL>", "maxResults": 15}}]}, "watch_start": {"description": "Start watching for Gmail message changes via push notifications", "inputSchema": {"type": "object", "properties": {"topicName": {"type": "string"}, "labelIds": {"type": "object"}, "labelFilterAction": {"type": "object"}}, "required": ["topicName"]}, "requiredScopes": ["https://mail.google.com/"], "idempotent": false, "safeMode": false, "deprecated": false, "examples": [{"name": "Watch all messages", "description": "Start watching for changes to all messages", "parameters": {"topicName": "projects/my-project/topics/gmail-notifications"}}, {"name": "Watch inbox only", "description": "Watch for changes only to inbox messages", "parameters": {"topicName": "projects/my-project/topics/gmail-notifications", "labelIds": ["INBOX"], "labelFilterAction": "include"}}]}, "watch_stop": {"description": "Stop watching for Gmail message changes", "inputSchema": {"type": "object", "properties": {}, "required": []}, "requiredScopes": ["https://mail.google.com/"], "idempotent": true, "safeMode": true, "deprecated": false, "examples": [{"name": "Stop watching", "description": "Stop all Gmail push notifications", "parameters": {}}]}}}}}