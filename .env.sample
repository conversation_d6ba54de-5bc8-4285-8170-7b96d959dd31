# Server Configuration
PORT=3000
NODE_ENV=development
LOG_LEVEL=info

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-client-secret
OAUTH_REDIRECT_URI=http://localhost:3000/auth/callback

# Service Account Configuration (for Domain-Wide Delegation)
SERVICE_ACCOUNT_JSON_PATH=./service-account.json
IMPERSONATE_USER_EMAIL=<EMAIL>

# Security & Encryption
ENCRYPTION_KEY=your-32-character-encryption-key-here
JWT_SECRET=your-jwt-secret-key-here

# Database Configuration
DB_URL=./data/workspace.db
DB_BACKUP_INTERVAL=3600000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# RBAC Configuration
ALLOWED_DOMAINS=yourdomain.com,anotherdomain.com
DEFAULT_SCOPES=https://www.googleapis.com/auth/userinfo.email,https://www.googleapis.com/auth/userinfo.profile
ROLE_MATRIX_PATH=./config/roles.json

# Google API Configuration
GOOGLE_API_RETRY_ATTEMPTS=3
GOOGLE_API_RETRY_DELAY=1000
GOOGLE_API_TIMEOUT=30000

# Audit & Logging
AUDIT_LOG_ENABLED=true
AUDIT_LOG_PATH=./logs/audit.log
REQUEST_LOG_ENABLED=true

# Feature Flags
ENABLE_PUSH_NOTIFICATIONS=false
ENABLE_CROSS_APP_MACROS=true
ENABLE_SAFE_MODE=false

# External Services
WEBHOOK_BASE_URL=https://your-domain.com
PUBSUB_TOPIC_NAME=workspace-notifications
