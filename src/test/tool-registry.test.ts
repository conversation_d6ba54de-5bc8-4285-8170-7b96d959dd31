import { describe, it, expect, beforeEach } from 'vitest';
import { z } from 'zod';
import { ToolRegistry } from '@/mcp/tool-registry';
import { testUtils } from './setup';
import type { MCPTool, MCPContext, MCPResult } from '@/types';

describe('ToolRegistry', () => {
  let registry: ToolRegistry;
  let testTool: MCPTool;

  beforeEach(() => {
    registry = ToolRegistry.getInstance();
    
    // Clear any existing tools
    const tools = registry.getAllTools();
    for (const [name] of tools) {
      // Note: In a real implementation, you'd want a method to unregister tools
      // For now, we'll work with the existing tools
    }

    testTool = {
      name: 'test_tool',
      description: 'A test tool for unit testing',
      inputSchema: z.object({
        message: z.string().min(1),
        count: z.number().optional().default(1),
      }),
      handler: async (params: unknown, context: MCPContext): Promise<MCPResult> => {
        const { message, count } = params as { message: string; count: number };
        return {
          success: true,
          data: {
            message: message.repeat(count),
            processedBy: context.userId,
          },
        };
      },
      requiredScopes: ['https://www.googleapis.com/auth/test'],
      idempotent: true,
      safeMode: true,
      examples: [
        {
          name: 'Basic usage',
          description: 'Test the tool with basic parameters',
          parameters: {
            message: 'Hello',
            count: 2,
          },
        },
      ],
    };
  });

  describe('registerTool', () => {
    it('should register a tool successfully', () => {
      registry.registerTool('test', testTool);
      
      const registeredTool = registry.getTool('test.test_tool');
      expect(registeredTool).toBeDefined();
      expect(registeredTool?.tool.name).toBe('test_tool');
      expect(registeredTool?.namespace).toBe('test');
    });

    it('should register tool with version and options', () => {
      registry.registerTool('test', testTool, '2.0.0', {
        deprecated: true,
        replacedBy: 'test.new_tool',
      });
      
      const registeredTool = registry.getTool('test.test_tool');
      expect(registeredTool?.version).toBe('2.0.0');
      expect(registeredTool?.deprecated).toBe(true);
      expect(registeredTool?.replacedBy).toBe('test.new_tool');
    });

    it('should add namespace to namespaces list', () => {
      registry.registerTool('test', testTool);
      
      const namespaces = registry.getNamespaces();
      expect(namespaces).toContain('test');
    });
  });

  describe('getTool', () => {
    beforeEach(() => {
      registry.registerTool('test', testTool);
    });

    it('should retrieve registered tool', () => {
      const tool = registry.getTool('test.test_tool');
      expect(tool).toBeDefined();
      expect(tool?.tool.name).toBe('test_tool');
    });

    it('should return undefined for non-existent tool', () => {
      const tool = registry.getTool('test.non_existent');
      expect(tool).toBeUndefined();
    });
  });

  describe('getToolsByNamespace', () => {
    beforeEach(() => {
      registry.registerTool('test', testTool);
      registry.registerTool('test', {
        ...testTool,
        name: 'another_tool',
      });
      registry.registerTool('other', testTool);
    });

    it('should return tools for specific namespace', () => {
      const testTools = registry.getToolsByNamespace('test');
      expect(testTools.size).toBe(2);
      expect(testTools.has('test.test_tool')).toBe(true);
      expect(testTools.has('test.another_tool')).toBe(true);
    });

    it('should return empty map for non-existent namespace', () => {
      const tools = registry.getToolsByNamespace('non_existent');
      expect(tools.size).toBe(0);
    });
  });

  describe('executeTool', () => {
    beforeEach(() => {
      registry.registerTool('test', testTool);
    });

    it('should execute tool successfully with valid parameters', async () => {
      const context = testUtils.createTestContext({
        scopes: ['https://www.googleapis.com/auth/test'],
        role: 'admin',
      });

      const result = await registry.executeTool('test.test_tool', {
        message: 'Hello',
        count: 3,
      }, context);

      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        message: 'HelloHelloHello',
        processedBy: context.userId,
      });
    });

    it('should return error for invalid parameters', async () => {
      const context = testUtils.createTestContext({
        scopes: ['https://www.googleapis.com/auth/test'],
        role: 'admin',
      });

      const result = await registry.executeTool('test.test_tool', {
        message: '', // Invalid: empty string
      }, context);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid parameters');
    });

    it('should return error for non-existent tool', async () => {
      const context = testUtils.createTestContext();

      const result = await registry.executeTool('test.non_existent', {}, context);

      expect(result.success).toBe(false);
      expect(result.error).toContain('not found');
    });

    it('should handle tool execution errors gracefully', async () => {
      const errorTool: MCPTool = {
        ...testTool,
        name: 'error_tool',
        handler: async () => {
          throw new Error('Tool execution failed');
        },
      };

      registry.registerTool('test', errorTool);

      const context = testUtils.createTestContext({
        scopes: ['https://www.googleapis.com/auth/test'],
        role: 'admin',
      });

      const result = await registry.executeTool('test.error_tool', {
        message: 'test',
      }, context);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Tool execution failed');
    });
  });

  describe('getToolManifest', () => {
    beforeEach(() => {
      registry.registerTool('test', testTool, '1.0.0');
    });

    it('should generate tool manifest', () => {
      const manifest = registry.getToolManifest();

      expect(manifest.version).toBe('1.0.0');
      expect(manifest.namespaces).toContain('test');
      expect(manifest.totalTools).toBeGreaterThan(0);
      
      const testToolInManifest = manifest.tools.find(t => t.name === 'test.test_tool');
      expect(testToolInManifest).toBeDefined();
      expect(testToolInManifest?.description).toBe(testTool.description);
      expect(testToolInManifest?.requiredScopes).toEqual(testTool.requiredScopes);
    });
  });

  describe('getToolStats', () => {
    beforeEach(() => {
      registry.registerTool('test', testTool);
      registry.registerTool('test', {
        ...testTool,
        name: 'another_tool',
        idempotent: false,
        safeMode: false,
      });
      registry.registerTool('other', testTool, '1.0.0', { deprecated: true });
    });

    it('should return accurate tool statistics', () => {
      const stats = registry.getToolStats();

      expect(stats.totalTools).toBeGreaterThanOrEqual(3);
      expect(stats.toolsByNamespace.test).toBeGreaterThanOrEqual(2);
      expect(stats.toolsByNamespace.other).toBeGreaterThanOrEqual(1);
      expect(stats.deprecatedTools).toBeGreaterThanOrEqual(1);
      expect(stats.safeModTools).toBeGreaterThanOrEqual(2);
      expect(stats.idempotentTools).toBeGreaterThanOrEqual(2);
    });
  });
});
