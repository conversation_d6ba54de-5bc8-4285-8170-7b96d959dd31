import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { config } from 'dotenv';
import { DatabaseManager } from '@/db/database';
import { ToolRegistry } from '@/mcp/tool-registry';
import { ConfigManager } from '@/utils/config';
import { EncryptionService } from '@/auth/encryption';
import { RBACService } from '@/auth/rbac';
import { RateLimiterService } from '@/auth/rate-limiter';

// Load test environment variables
config({ path: '.env.test' });

// Set test environment
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error'; // Reduce log noise in tests
process.env.DB_URL = ':memory:'; // Use in-memory database for tests

let db: DatabaseManager;

beforeAll(async () => {
  // Initialize test database
  db = DatabaseManager.getInstance(':memory:');

  // Initialize configuration for tests
  const configManager = ConfigManager.getInstance();

  // Initialize services with test configuration
  EncryptionService.getInstance('test-encryption-key-32-characters-long');
  RBACService.getInstance();
  RateLimiterService.getInstance(configManager.getRateLimitConfig());
});

afterAll(async () => {
  // Clean up database connection
  if (db) {
    db.close();
  }
});

beforeEach(async () => {
  // Clean up database before each test
  const database = db.getDatabase();

  // Get all table names
  const tables = database.prepare(`
    SELECT name FROM sqlite_master
    WHERE type='table' AND name NOT LIKE 'sqlite_%'
  `).all() as Array<{ name: string }>;

  // Clear all tables
  for (const table of tables) {
    database.prepare(`DELETE FROM ${table.name}`).run();
  }

  // Create a test user for foreign key constraints
  database.prepare(`
    INSERT INTO users (id, email, name, role, domains, scopes)
    VALUES (?, ?, ?, ?, ?, ?)
  `).run(
    'test-user-id',
    '<EMAIL>',
    'Test User',
    'viewer',
    JSON.stringify(['example.com']),
    JSON.stringify(['https://www.googleapis.com/auth/gmail.readonly'])
  );
});

afterEach(async () => {
  // Clean up any test artifacts
});

// Test utilities
export const testUtils = {
  createTestUser: () => ({
    email: '<EMAIL>',
    name: 'Test User',
    role: 'viewer' as const,
    domains: ['example.com'],
    scopes: ['https://www.googleapis.com/auth/gmail.readonly'],
  }),
  
  createTestContext: (overrides = {}) => ({
    userId: 'test-user-id',
    userEmail: '<EMAIL>',
    role: 'viewer' as const,
    scopes: ['https://www.googleapis.com/auth/gmail.readonly'],
    requestId: 'test-request-id',
    ...overrides,
  }),
  
  createTestResource: () => ({
    type: 'gmail_message' as const,
    app: 'gmail',
    title: 'Test Message',
    external_id: 'test-message-id',
    url: 'https://mail.google.com/mail/u/0/#inbox/test-message-id',
    created_by: 'test-user-id',
    metadata: {
      subject: 'Test Subject',
      from: '<EMAIL>',
    },
  }),
};
