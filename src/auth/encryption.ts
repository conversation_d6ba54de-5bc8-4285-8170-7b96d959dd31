import CryptoJS from 'crypto-js';
import { logger } from '@/utils/logger';

export class EncryptionService {
  private static instance: EncryptionService;
  private encryptionKey: string;

  private constructor(encryptionKey: string) {
    if (!encryptionKey || encryptionKey.length < 32) {
      throw new Error('Encryption key must be at least 32 characters long');
    }
    this.encryptionKey = encryptionKey;
  }

  public static getInstance(encryptionKey?: string): EncryptionService {
    if (!EncryptionService.instance) {
      if (!encryptionKey) {
        throw new Error('Encryption key is required for first initialization');
      }
      EncryptionService.instance = new EncryptionService(encryptionKey);
    }
    return EncryptionService.instance;
  }

  public encrypt(data: string | object): string {
    try {
      const plaintext = typeof data === 'string' ? data : JSON.stringify(data);
      const encrypted = CryptoJS.AES.encrypt(plaintext, this.encryptionKey).toString();
      return encrypted;
    } catch (error) {
      logger.error('Encryption failed:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  public decrypt(encryptedData: string): string {
    try {
      const decrypted = CryptoJS.AES.decrypt(encryptedData, this.encryptionKey);
      const plaintext = decrypted.toString(CryptoJS.enc.Utf8);
      
      if (!plaintext) {
        throw new Error('Decryption resulted in empty string');
      }
      
      return plaintext;
    } catch (error) {
      logger.error('Decryption failed:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  public encryptObject<T>(obj: T): string {
    return this.encrypt(JSON.stringify(obj));
  }

  public decryptObject<T>(encryptedData: string): T {
    const decrypted = this.decrypt(encryptedData);
    try {
      return JSON.parse(decrypted) as T;
    } catch (error) {
      logger.error('Failed to parse decrypted JSON:', error);
      throw new Error('Failed to parse decrypted data as JSON');
    }
  }

  public hash(data: string): string {
    return CryptoJS.SHA256(data).toString();
  }

  public generateRandomKey(length: number = 32): string {
    return CryptoJS.lib.WordArray.random(length).toString();
  }

  public verifyHash(data: string, hash: string): boolean {
    return this.hash(data) === hash;
  }
}
