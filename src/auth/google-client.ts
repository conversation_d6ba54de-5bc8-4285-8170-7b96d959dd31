import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import { JWT } from 'google-auth-library';
import { readFileSync } from 'fs';
import { logger } from '@/utils/logger';
import type { 
  GoogleClientConfig, 
  ServiceAccountConfig, 
  OAuthTokens,
  GoogleConfig 
} from '@/types';

export class GoogleClientFactory {
  private static instance: GoogleClientFactory;
  private config: GoogleConfig;
  private serviceAccountConfig?: ServiceAccountConfig;

  private constructor(config: GoogleConfig) {
    this.config = config;
    this.loadServiceAccountConfig();
  }

  public static getInstance(config?: GoogleConfig): GoogleClientFactory {
    if (!GoogleClientFactory.instance) {
      if (!config) {
        throw new Error('Google config is required for first initialization');
      }
      GoogleClientFactory.instance = new GoogleClientFactory(config);
    }
    return GoogleClientFactory.instance;
  }

  private loadServiceAccountConfig(): void {
    if (this.config.serviceAccountPath) {
      try {
        const serviceAccountJson = readFileSync(this.config.serviceAccountPath, 'utf-8');
        this.serviceAccountConfig = JSON.parse(serviceAccountJson) as ServiceAccountConfig;
        logger.info('Service account configuration loaded successfully');
      } catch (error) {
        logger.warn('Failed to load service account configuration:', error);
      }
    }
  }

  public createOAuth2Client(tokens?: OAuthTokens): OAuth2Client {
    const client = new OAuth2Client(
      this.config.clientId,
      this.config.clientSecret,
      this.config.redirectUri
    );

    if (tokens) {
      client.setCredentials({
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        scope: tokens.scope,
        token_type: tokens.token_type,
        expiry_date: tokens.expires_at,
      });
    }

    return client;
  }

  public createServiceAccountClient(
    scopes: string[],
    impersonateUser?: string
  ): JWT | null {
    if (!this.serviceAccountConfig) {
      logger.warn('Service account configuration not available');
      return null;
    }

    const client = new JWT({
      email: this.serviceAccountConfig.client_email,
      key: this.serviceAccountConfig.private_key,
      scopes,
      subject: impersonateUser || this.config.impersonateUserEmail,
    });

    return client;
  }

  public async getAuthenticatedClient(
    clientConfig: GoogleClientConfig,
    tokens?: OAuthTokens
  ): Promise<OAuth2Client | JWT> {
    const { userId, scopes, impersonateUser } = clientConfig;

    // Try service account with domain-wide delegation first
    if (this.serviceAccountConfig && impersonateUser) {
      const serviceClient = this.createServiceAccountClient(scopes, impersonateUser);
      if (serviceClient) {
        try {
          await serviceClient.authorize();
          logger.info(`Service account client authorized for user: ${impersonateUser}`);
          return serviceClient;
        } catch (error) {
          logger.warn('Service account authorization failed, falling back to OAuth:', error);
        }
      }
    }

    // Fall back to OAuth2
    if (!tokens) {
      throw new Error(`No tokens available for user ${userId} and service account failed`);
    }

    const oauthClient = this.createOAuth2Client(tokens);
    
    try {
      // Check if token needs refresh
      const tokenInfo = await oauthClient.getTokenInfo(tokens.access_token);
      logger.info(`OAuth token valid for user: ${userId}`, { 
        scopes: tokenInfo.scopes,
        expiresIn: tokenInfo.expiry_date 
      });
    } catch (error) {
      // Try to refresh token
      try {
        const { credentials } = await oauthClient.refreshAccessToken();
        oauthClient.setCredentials(credentials);
        logger.info(`Token refreshed for user: ${userId}`);
      } catch (refreshError) {
        logger.error(`Token refresh failed for user: ${userId}`, refreshError);
        throw new Error('Authentication failed: invalid or expired tokens');
      }
    }

    return oauthClient;
  }

  public getAuthUrl(scopes: string[], state?: string): string {
    const client = this.createOAuth2Client();
    return client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      state,
      prompt: 'consent',
    });
  }

  public async exchangeCodeForTokens(code: string): Promise<OAuthTokens> {
    const client = this.createOAuth2Client();
    
    try {
      const { tokens } = await client.getToken(code);
      
      if (!tokens.access_token || !tokens.refresh_token) {
        throw new Error('Invalid token response from Google');
      }

      return {
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        scope: tokens.scope || '',
        token_type: tokens.token_type || 'Bearer',
        expires_at: tokens.expiry_date || Date.now() + 3600000, // 1 hour default
      };
    } catch (error) {
      logger.error('Failed to exchange code for tokens:', error);
      throw new Error('Failed to exchange authorization code for tokens');
    }
  }

  public async getUserInfo(client: OAuth2Client | JWT): Promise<{
    id: string;
    email: string;
    name: string;
    picture?: string;
  }> {
    try {
      const oauth2 = google.oauth2({ version: 'v2', auth: client });
      const { data } = await oauth2.userinfo.get();
      
      if (!data.id || !data.email || !data.name) {
        throw new Error('Incomplete user information from Google');
      }

      return {
        id: data.id,
        email: data.email,
        name: data.name,
        picture: data.picture || undefined,
      };
    } catch (error) {
      logger.error('Failed to get user info:', error);
      throw new Error('Failed to retrieve user information');
    }
  }

  public async revokeTokens(tokens: OAuthTokens): Promise<void> {
    const client = this.createOAuth2Client(tokens);
    
    try {
      await client.revokeCredentials();
      logger.info('Tokens revoked successfully');
    } catch (error) {
      logger.error('Failed to revoke tokens:', error);
      throw new Error('Failed to revoke tokens');
    }
  }
}
