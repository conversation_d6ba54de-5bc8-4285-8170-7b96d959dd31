import { readFileSync, existsSync } from 'fs';
import { logger } from '@/utils/logger';
import type { UserRole, MCPContext } from '@/types';

export interface RolePermissions {
  allowedTools: string[];
  allowedScopes: string[];
  rateLimits: {
    requestsPerMinute: number;
    requestsPerHour: number;
    requestsPerDay: number;
  };
  restrictions: {
    readOnly?: boolean;
    maxFileSize?: number;
    allowedDomains?: string[];
    allowedResourceTypes?: string[];
  };
}

export interface RoleMatrix {
  [key: string]: RolePermissions;
}

export class RBACService {
  private static instance: RBACService;
  private roleMatrix: RoleMatrix;
  private defaultRoleMatrix: RoleMatrix = {
    [UserRole.ADMIN]: {
      allowedTools: ['*'], // All tools
      allowedScopes: [
        'https://mail.google.com/',
        'https://www.googleapis.com/auth/drive',
        'https://www.googleapis.com/auth/documents',
        'https://www.googleapis.com/auth/spreadsheets',
        'https://www.googleapis.com/auth/presentations',
        'https://www.googleapis.com/auth/calendar',
        'https://www.googleapis.com/auth/contacts',
        'https://www.googleapis.com/auth/tasks',
        'https://www.googleapis.com/auth/keep',
        'https://www.googleapis.com/auth/forms.body',
        'https://www.googleapis.com/auth/forms.responses.readonly',
      ],
      rateLimits: {
        requestsPerMinute: 1000,
        requestsPerHour: 10000,
        requestsPerDay: 100000,
      },
      restrictions: {
        readOnly: false,
        maxFileSize: 100 * 1024 * 1024, // 100MB
      },
    },
    [UserRole.EDITOR]: {
      allowedTools: [
        'gmail.*',
        'drive.*',
        'docs.*',
        'sheets.*',
        'slides.*',
        'forms.*',
        'calendar.*',
        'contacts.*',
        'tasks.*',
        'keep.*',
      ],
      allowedScopes: [
        'https://mail.google.com/',
        'https://www.googleapis.com/auth/drive',
        'https://www.googleapis.com/auth/documents',
        'https://www.googleapis.com/auth/spreadsheets',
        'https://www.googleapis.com/auth/presentations',
        'https://www.googleapis.com/auth/calendar',
        'https://www.googleapis.com/auth/contacts',
        'https://www.googleapis.com/auth/tasks',
        'https://www.googleapis.com/auth/keep',
        'https://www.googleapis.com/auth/forms.body',
        'https://www.googleapis.com/auth/forms.responses.readonly',
      ],
      rateLimits: {
        requestsPerMinute: 500,
        requestsPerHour: 5000,
        requestsPerDay: 50000,
      },
      restrictions: {
        readOnly: false,
        maxFileSize: 50 * 1024 * 1024, // 50MB
      },
    },
    [UserRole.VIEWER]: {
      allowedTools: [
        'gmail.search',
        'gmail.get_message',
        'gmail.list_threads',
        'drive.search',
        'drive.download',
        'docs.read',
        'sheets.read_range',
        'slides.read',
        'forms.list_responses',
        'forms.get_response',
        'calendar.list_events',
        'contacts.search',
        'contacts.list',
        'tasks.list',
        'keep.search',
      ],
      allowedScopes: [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/drive.readonly',
        'https://www.googleapis.com/auth/documents.readonly',
        'https://www.googleapis.com/auth/spreadsheets.readonly',
        'https://www.googleapis.com/auth/presentations.readonly',
        'https://www.googleapis.com/auth/calendar.readonly',
        'https://www.googleapis.com/auth/contacts.readonly',
        'https://www.googleapis.com/auth/tasks.readonly',
        'https://www.googleapis.com/auth/keep.readonly',
        'https://www.googleapis.com/auth/forms.responses.readonly',
      ],
      rateLimits: {
        requestsPerMinute: 100,
        requestsPerHour: 1000,
        requestsPerDay: 10000,
      },
      restrictions: {
        readOnly: true,
        maxFileSize: 10 * 1024 * 1024, // 10MB
      },
    },
  };

  private constructor(roleMatrixPath?: string) {
    this.loadRoleMatrix(roleMatrixPath);
  }

  public static getInstance(roleMatrixPath?: string): RBACService {
    if (!RBACService.instance) {
      RBACService.instance = new RBACService(roleMatrixPath);
    }
    return RBACService.instance;
  }

  private loadRoleMatrix(roleMatrixPath?: string): void {
    if (roleMatrixPath && existsSync(roleMatrixPath)) {
      try {
        const roleMatrixJson = readFileSync(roleMatrixPath, 'utf-8');
        const customRoleMatrix = JSON.parse(roleMatrixJson) as RoleMatrix;
        
        // Merge with default role matrix
        this.roleMatrix = { ...this.defaultRoleMatrix };
        for (const [role, permissions] of Object.entries(customRoleMatrix)) {
          this.roleMatrix[role] = {
            ...this.defaultRoleMatrix[role],
            ...permissions,
          };
        }
        
        logger.info('Custom role matrix loaded successfully');
      } catch (error) {
        logger.warn('Failed to load custom role matrix, using defaults:', error);
        this.roleMatrix = this.defaultRoleMatrix;
      }
    } else {
      this.roleMatrix = this.defaultRoleMatrix;
      logger.info('Using default role matrix');
    }
  }

  public canExecuteTool(context: MCPContext, toolName: string): boolean {
    const permissions = this.roleMatrix[context.role];
    if (!permissions) {
      logger.warn(`Unknown role: ${context.role}`);
      return false;
    }

    // Check if user has access to all tools
    if (permissions.allowedTools.includes('*')) {
      return true;
    }

    // Check exact match
    if (permissions.allowedTools.includes(toolName)) {
      return true;
    }

    // Check wildcard patterns
    return permissions.allowedTools.some(pattern => {
      if (pattern.endsWith('*')) {
        const prefix = pattern.slice(0, -1);
        return toolName.startsWith(prefix);
      }
      return false;
    });
  }

  public hasRequiredScopes(context: MCPContext, requiredScopes: string[]): boolean {
    const permissions = this.roleMatrix[context.role];
    if (!permissions) {
      return false;
    }

    // Check if user has all required scopes
    return requiredScopes.every(scope => 
      permissions.allowedScopes.includes(scope) || context.scopes.includes(scope)
    );
  }

  public getRateLimits(role: UserRole): RolePermissions['rateLimits'] {
    const permissions = this.roleMatrix[role];
    return permissions?.rateLimits || this.defaultRoleMatrix[UserRole.VIEWER].rateLimits;
  }

  public getRestrictions(role: UserRole): RolePermissions['restrictions'] {
    const permissions = this.roleMatrix[role];
    return permissions?.restrictions || this.defaultRoleMatrix[UserRole.VIEWER].restrictions;
  }

  public isReadOnlyRole(role: UserRole): boolean {
    const restrictions = this.getRestrictions(role);
    return restrictions.readOnly === true;
  }

  public getMaxFileSize(role: UserRole): number {
    const restrictions = this.getRestrictions(role);
    return restrictions.maxFileSize || 10 * 1024 * 1024; // 10MB default
  }

  public canAccessDomain(role: UserRole, domain: string): boolean {
    const restrictions = this.getRestrictions(role);
    if (!restrictions.allowedDomains) {
      return true; // No domain restrictions
    }
    return restrictions.allowedDomains.includes(domain);
  }

  public canAccessResourceType(role: UserRole, resourceType: string): boolean {
    const restrictions = this.getRestrictions(role);
    if (!restrictions.allowedResourceTypes) {
      return true; // No resource type restrictions
    }
    return restrictions.allowedResourceTypes.includes(resourceType);
  }

  public validateContext(context: MCPContext, toolName: string, requiredScopes: string[]): {
    allowed: boolean;
    reason?: string;
  } {
    // Check tool access
    if (!this.canExecuteTool(context, toolName)) {
      return {
        allowed: false,
        reason: `Role ${context.role} does not have access to tool ${toolName}`,
      };
    }

    // Check scopes
    if (!this.hasRequiredScopes(context, requiredScopes)) {
      return {
        allowed: false,
        reason: `Role ${context.role} does not have required scopes: ${requiredScopes.join(', ')}`,
      };
    }

    return { allowed: true };
  }
}
