import { RateLimiterMemory, RateLimiterRes } from 'rate-limiter-flexible';
import { DatabaseManager } from '@/db/database';
import { RBACService } from '@/auth/rbac';
import { logger } from '@/utils/logger';
import type { UserRole, MCPContext, RateLimitConfig } from '@/types';

export interface RateLimitResult {
  allowed: boolean;
  remainingPoints?: number;
  msBeforeNext?: number;
  totalHits?: number;
}

export class RateLimiterService {
  private static instance: RateLimiterService;
  private memoryLimiters: Map<string, RateLimiterMemory> = new Map();
  private db = DatabaseManager.getInstance().getDatabase();
  private rbac = RBACService.getInstance();

  private constructor(private config: RateLimitConfig) {
    this.initializeMemoryLimiters();
  }

  public static getInstance(config?: RateLimitConfig): RateLimiterService {
    if (!RateLimiterService.instance) {
      if (!config) {
        throw new Error('Rate limit config is required for first initialization');
      }
      RateLimiterService.instance = new RateLimiterService(config);
    }
    return RateLimiterService.instance;
  }

  private initializeMemoryLimiters(): void {
    // Create limiters for different time windows
    const windows = [
      { key: 'minute', duration: 60, points: 100 },
      { key: 'hour', duration: 3600, points: 1000 },
      { key: 'day', duration: 86400, points: 10000 },
    ];

    for (const window of windows) {
      this.memoryLimiters.set(
        window.key,
        new RateLimiterMemory({
          keyGenerator: (req: { userId: string; toolName: string }) =>
            `${req.userId}:${req.toolName}:${window.key}`,
          points: window.points,
          duration: window.duration,
          blockDuration: window.duration,
        })
      );
    }
  }

  public async checkRateLimit(
    context: MCPContext,
    toolName: string
  ): Promise<RateLimitResult> {
    try {
      const rateLimits = this.rbac.getRateLimits(context.role);
      const key = this.config.keyGenerator
        ? this.config.keyGenerator(context.userId, toolName)
        : `${context.userId}:${toolName}`;

      // Check minute limit
      const minuteLimiter = this.memoryLimiters.get('minute');
      if (minuteLimiter) {
        try {
          const minuteRes = await minuteLimiter.consume({ userId: context.userId, toolName });
          if (minuteRes.remainingPoints < 0) {
            return {
              allowed: false,
              remainingPoints: minuteRes.remainingPoints,
              msBeforeNext: minuteRes.msBeforeNext,
              totalHits: minuteRes.totalHits,
            };
          }
        } catch (rejRes) {
          const res = rejRes as RateLimiterRes;
          return {
            allowed: false,
            remainingPoints: res.remainingPoints,
            msBeforeNext: res.msBeforeNext,
            totalHits: res.totalHits,
          };
        }
      }

      // Check hour limit
      const hourLimiter = this.memoryLimiters.get('hour');
      if (hourLimiter) {
        try {
          const hourRes = await hourLimiter.consume({ userId: context.userId, toolName });
          if (hourRes.remainingPoints < 0) {
            return {
              allowed: false,
              remainingPoints: hourRes.remainingPoints,
              msBeforeNext: hourRes.msBeforeNext,
              totalHits: hourRes.totalHits,
            };
          }
        } catch (rejRes) {
          const res = rejRes as RateLimiterRes;
          return {
            allowed: false,
            remainingPoints: res.remainingPoints,
            msBeforeNext: res.msBeforeNext,
            totalHits: res.totalHits,
          };
        }
      }

      // Check day limit
      const dayLimiter = this.memoryLimiters.get('day');
      if (dayLimiter) {
        try {
          const dayRes = await dayLimiter.consume({ userId: context.userId, toolName });
          if (dayRes.remainingPoints < 0) {
            return {
              allowed: false,
              remainingPoints: dayRes.remainingPoints,
              msBeforeNext: dayRes.msBeforeNext,
              totalHits: dayRes.totalHits,
            };
          }
        } catch (rejRes) {
          const res = rejRes as RateLimiterRes;
          return {
            allowed: false,
            remainingPoints: res.remainingPoints,
            msBeforeNext: res.msBeforeNext,
            totalHits: res.totalHits,
          };
        }
      }

      // Update database rate limit record
      await this.updateDatabaseRateLimit(context.userId, toolName);

      return { allowed: true };
    } catch (error) {
      logger.error('Rate limit check failed:', error);
      // Fail open - allow request if rate limiting fails
      return { allowed: true };
    }
  }

  private async updateDatabaseRateLimit(userId: string, toolName: string): Promise<void> {
    const now = Date.now();
    const windowStart = Math.floor(now / this.config.windowMs) * this.config.windowMs;

    try {
      // Try to update existing record
      const updateStmt = this.db.prepare(`
        UPDATE rate_limits 
        SET request_count = request_count + 1, updated_at = datetime('now')
        WHERE user_id = ? AND tool_name = ? AND window_start = ?
      `);
      
      const result = updateStmt.run(userId, toolName, windowStart);

      // If no existing record, create new one
      if (result.changes === 0) {
        const insertStmt = this.db.prepare(`
          INSERT INTO rate_limits (id, user_id, tool_name, window_start, request_count)
          VALUES (?, ?, ?, ?, 1)
        `);
        
        const id = `${userId}:${toolName}:${windowStart}`;
        insertStmt.run(id, userId, toolName, windowStart);
      }
    } catch (error) {
      logger.error('Failed to update database rate limit:', error);
      // Don't throw - rate limiting should not break the request
    }
  }

  public async getRateLimitStatus(
    userId: string,
    toolName?: string
  ): Promise<{
    userId: string;
    toolName?: string;
    currentWindow: {
      start: number;
      requestCount: number;
    };
    limits: {
      requestsPerMinute: number;
      requestsPerHour: number;
      requestsPerDay: number;
    };
  }> {
    const now = Date.now();
    const windowStart = Math.floor(now / this.config.windowMs) * this.config.windowMs;

    let query = `
      SELECT tool_name, request_count 
      FROM rate_limits 
      WHERE user_id = ? AND window_start = ?
    `;
    const params: unknown[] = [userId, windowStart];

    if (toolName) {
      query += ' AND tool_name = ?';
      params.push(toolName);
    }

    const stmt = this.db.prepare(query);
    const records = stmt.all(...params) as Array<{
      tool_name: string;
      request_count: number;
    }>;

    const requestCount = records.reduce((sum, record) => sum + record.request_count, 0);

    // Get user role to determine limits
    const userStmt = this.db.prepare('SELECT role FROM users WHERE id = ?');
    const userRecord = userStmt.get(userId) as { role: UserRole } | undefined;
    const role = userRecord?.role || UserRole.VIEWER;
    const limits = this.rbac.getRateLimits(role);

    return {
      userId,
      toolName,
      currentWindow: {
        start: windowStart,
        requestCount,
      },
      limits,
    };
  }

  public async resetRateLimit(userId: string, toolName?: string): Promise<void> {
    try {
      let query = 'DELETE FROM rate_limits WHERE user_id = ?';
      const params: unknown[] = [userId];

      if (toolName) {
        query += ' AND tool_name = ?';
        params.push(toolName);
      }

      const stmt = this.db.prepare(query);
      const result = stmt.run(...params);

      logger.info(`Reset rate limits for user ${userId}`, {
        toolName,
        recordsDeleted: result.changes,
      });

      // Also reset memory limiters
      for (const limiter of this.memoryLimiters.values()) {
        if (toolName) {
          await limiter.delete(`${userId}:${toolName}:minute`);
          await limiter.delete(`${userId}:${toolName}:hour`);
          await limiter.delete(`${userId}:${toolName}:day`);
        } else {
          // Reset all tools for user (this is more complex, would need to track keys)
          logger.warn('Resetting all memory limiters for user not implemented');
        }
      }
    } catch (error) {
      logger.error('Failed to reset rate limits:', error);
      throw error;
    }
  }

  public async cleanupExpiredRecords(): Promise<void> {
    const cutoff = Date.now() - 24 * 60 * 60 * 1000; // 24 hours ago
    
    try {
      const stmt = this.db.prepare('DELETE FROM rate_limits WHERE window_start < ?');
      const result = stmt.run(cutoff);
      
      logger.info(`Cleaned up ${result.changes} expired rate limit records`);
    } catch (error) {
      logger.error('Failed to cleanup expired rate limit records:', error);
    }
  }
}
