import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import { JWT } from 'google-auth-library';
import { createReadStream } from 'fs';
import { logger } from '@/utils/logger';
import { GoogleClientFactory } from '@/auth/google-client';
import { UserRepository } from '@/db/repositories/user';
import { ResourceRepository } from '@/db/repositories/resource';
import type { MCPContext, ResourceType } from '@/types';

export interface GmailAttachment {
  filename: string;
  content: string; // base64 encoded
  mimeType: string;
  size?: number;
}

export interface GmailMessage {
  id: string;
  threadId: string;
  labelIds: string[];
  snippet: string;
  historyId: string;
  internalDate: string;
  payload: {
    partId: string;
    mimeType: string;
    filename: string;
    headers: Array<{
      name: string;
      value: string;
    }>;
    body: {
      attachmentId?: string;
      size: number;
      data?: string;
    };
    parts?: any[];
  };
  sizeEstimate: number;
}

export interface GmailThread {
  id: string;
  historyId: string;
  messages: GmailMessage[];
}

export class GmailService {
  private googleClientFactory?: GoogleClientFactory;
  private userRepo = new UserRepository();
  private resourceRepo = new ResourceRepository();

  private getGoogleClientFactory(): GoogleClientFactory {
    if (!this.googleClientFactory) {
      this.googleClientFactory = GoogleClientFactory.getInstance();
    }
    return this.googleClientFactory;
  }

  public async getGmailClient(context: MCPContext): Promise<any> {
    const tokens = await this.userRepo.getTokens(context.userId);
    const client = await this.getGoogleClientFactory().getAuthenticatedClient(
      {
        userId: context.userId,
        scopes: context.scopes,
      },
      tokens || undefined
    );

    return google.gmail({ version: 'v1', auth: client });
  }

  public async sendEmail(
    context: MCPContext,
    params: {
      to: string[];
      subject: string;
      html?: string;
      text?: string;
      cc?: string[];
      bcc?: string[];
      attachments?: GmailAttachment[];
      threadId?: string;
    }
  ): Promise<{ messageId: string; threadId: string }> {
    try {
      const gmail = await this.getGmailClient(context);
      
      // Build email message
      const messageParts = [];
      const boundary = `boundary_${Date.now()}`;
      
      // Headers
      const headers = [
        `To: ${params.to.join(', ')}`,
        `Subject: ${params.subject}`,
      ];
      
      if (params.cc && params.cc.length > 0) {
        headers.push(`Cc: ${params.cc.join(', ')}`);
      }
      
      if (params.bcc && params.bcc.length > 0) {
        headers.push(`Bcc: ${params.bcc.join(', ')}`);
      }
      
      headers.push(`Content-Type: multipart/mixed; boundary="${boundary}"`);
      headers.push('');
      
      messageParts.push(headers.join('\r\n'));
      
      // Body
      messageParts.push(`--${boundary}`);
      if (params.html) {
        messageParts.push('Content-Type: text/html; charset=utf-8');
        messageParts.push('');
        messageParts.push(params.html);
      } else if (params.text) {
        messageParts.push('Content-Type: text/plain; charset=utf-8');
        messageParts.push('');
        messageParts.push(params.text);
      }
      
      // Attachments
      if (params.attachments) {
        for (const attachment of params.attachments) {
          messageParts.push(`--${boundary}`);
          messageParts.push(`Content-Type: ${attachment.mimeType}`);
          messageParts.push(`Content-Disposition: attachment; filename="${attachment.filename}"`);
          messageParts.push('Content-Transfer-Encoding: base64');
          messageParts.push('');
          messageParts.push(attachment.content);
        }
      }
      
      messageParts.push(`--${boundary}--`);
      
      const rawMessage = messageParts.join('\r\n');
      const encodedMessage = Buffer.from(rawMessage).toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '');
      
      const requestBody: any = {
        raw: encodedMessage,
      };
      
      if (params.threadId) {
        requestBody.threadId = params.threadId;
      }
      
      const response = await gmail.users.messages.send({
        userId: 'me',
        requestBody,
      });
      
      const message = response.data;
      
      // Store in resource repository
      await this.resourceRepo.create({
        type: 'gmail_message' as ResourceType,
        app: 'gmail',
        title: params.subject,
        external_id: message.id!,
        url: `https://mail.google.com/mail/u/0/#inbox/${message.id}`,
        created_by: context.userId,
        metadata: {
          to: params.to,
          cc: params.cc,
          bcc: params.bcc,
          hasAttachments: !!params.attachments?.length,
        },
      });
      
      logger.info('Email sent successfully', {
        messageId: message.id,
        threadId: message.threadId,
        userId: context.userId,
        subject: params.subject,
      });
      
      return {
        messageId: message.id!,
        threadId: message.threadId!,
      };
      
    } catch (error) {
      logger.error('Failed to send email:', error);
      throw new Error(`Failed to send email: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async searchMessages(
    context: MCPContext,
    params: {
      query: string;
      maxResults?: number;
      pageToken?: string;
    }
  ): Promise<{
    messages: Array<{ id: string; threadId: string }>;
    nextPageToken?: string;
    resultSizeEstimate: number;
  }> {
    try {
      const gmail = await this.getGmailClient(context);
      
      const response = await gmail.users.messages.list({
        userId: 'me',
        q: params.query,
        maxResults: params.maxResults || 10,
        pageToken: params.pageToken,
      });
      
      const messages = response.data.messages || [];
      
      logger.info('Messages searched successfully', {
        query: params.query,
        resultCount: messages.length,
        userId: context.userId,
      });
      
      return {
        messages,
        nextPageToken: response.data.nextPageToken,
        resultSizeEstimate: response.data.resultSizeEstimate || 0,
      };
      
    } catch (error) {
      logger.error('Failed to search messages:', error);
      throw new Error(`Failed to search messages: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async getMessage(
    context: MCPContext,
    params: {
      id: string;
      format?: 'minimal' | 'full' | 'raw' | 'metadata';
    }
  ): Promise<GmailMessage> {
    try {
      const gmail = await this.getGmailClient(context);
      
      const response = await gmail.users.messages.get({
        userId: 'me',
        id: params.id,
        format: params.format || 'full',
      });
      
      const message = response.data as GmailMessage;
      
      logger.info('Message retrieved successfully', {
        messageId: params.id,
        userId: context.userId,
        format: params.format,
      });
      
      return message;
      
    } catch (error) {
      logger.error('Failed to get message:', error);
      throw new Error(`Failed to get message: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async modifyLabels(
    context: MCPContext,
    params: {
      messageId: string;
      addLabelIds?: string[];
      removeLabelIds?: string[];
    }
  ): Promise<{ id: string; labelIds: string[] }> {
    try {
      const gmail = await this.getGmailClient(context);
      
      const response = await gmail.users.messages.modify({
        userId: 'me',
        id: params.messageId,
        requestBody: {
          addLabelIds: params.addLabelIds,
          removeLabelIds: params.removeLabelIds,
        },
      });
      
      const message = response.data;
      
      logger.info('Message labels modified successfully', {
        messageId: params.messageId,
        addedLabels: params.addLabelIds,
        removedLabels: params.removeLabelIds,
        userId: context.userId,
      });
      
      return {
        id: message.id!,
        labelIds: message.labelIds || [],
      };
      
    } catch (error) {
      logger.error('Failed to modify labels:', error);
      throw new Error(`Failed to modify labels: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async listThreads(
    context: MCPContext,
    params: {
      query?: string;
      maxResults?: number;
      pageToken?: string;
    }
  ): Promise<{
    threads: Array<{ id: string; historyId: string; snippet: string }>;
    nextPageToken?: string;
    resultSizeEstimate: number;
  }> {
    try {
      const gmail = await this.getGmailClient(context);
      
      const response = await gmail.users.threads.list({
        userId: 'me',
        q: params.query,
        maxResults: params.maxResults || 10,
        pageToken: params.pageToken,
      });
      
      const threads = response.data.threads || [];
      
      // Get snippet for each thread
      const threadsWithSnippets = await Promise.all(
        threads.map(async (thread) => {
          try {
            const threadDetail = await gmail.users.threads.get({
              userId: 'me',
              id: thread.id,
              format: 'metadata',
            });
            
            const firstMessage = threadDetail.data.messages?.[0];
            const snippet = firstMessage?.snippet || '';
            
            return {
              id: thread.id!,
              historyId: thread.historyId!,
              snippet,
            };
          } catch {
            return {
              id: thread.id!,
              historyId: thread.historyId!,
              snippet: '',
            };
          }
        })
      );
      
      logger.info('Threads listed successfully', {
        query: params.query,
        resultCount: threads.length,
        userId: context.userId,
      });
      
      return {
        threads: threadsWithSnippets,
        nextPageToken: response.data.nextPageToken,
        resultSizeEstimate: response.data.resultSizeEstimate || 0,
      };
      
    } catch (error) {
      logger.error('Failed to list threads:', error);
      throw new Error(`Failed to list threads: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async watchMessages(
    context: MCPContext,
    params: {
      topicName: string;
      labelIds?: string[];
      labelFilterAction?: 'include' | 'exclude';
    }
  ): Promise<{ historyId: string; expiration: string }> {
    try {
      const gmail = await this.getGmailClient(context);
      
      const response = await gmail.users.watch({
        userId: 'me',
        requestBody: {
          topicName: params.topicName,
          labelIds: params.labelIds,
          labelFilterAction: params.labelFilterAction,
        },
      });
      
      const watchResponse = response.data;
      
      logger.info('Watch started successfully', {
        topicName: params.topicName,
        historyId: watchResponse.historyId,
        expiration: watchResponse.expiration,
        userId: context.userId,
      });
      
      return {
        historyId: watchResponse.historyId!,
        expiration: watchResponse.expiration!,
      };
      
    } catch (error) {
      logger.error('Failed to start watch:', error);
      throw new Error(`Failed to start watch: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async stopWatch(context: MCPContext): Promise<void> {
    try {
      const gmail = await this.getGmailClient(context);
      
      await gmail.users.stop({
        userId: 'me',
      });
      
      logger.info('Watch stopped successfully', {
        userId: context.userId,
      });
      
    } catch (error) {
      logger.error('Failed to stop watch:', error);
      throw new Error(`Failed to stop watch: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
