import { z } from 'zod';
import { GmailService } from './service';
import type { <PERSON>P<PERSON>ool, MCPContext, MCPResult } from '@/types';

const gmailService = new GmailService();

// Send Email Tool
export const sendEmailTool: MCPTool = {
  name: 'send_email',
  description: 'Send an email through Gmail with optional attachments and threading support',
  inputSchema: z.object({
    to: z.array(z.string().email()).min(1).describe('Array of recipient email addresses'),
    subject: z.string().min(1).describe('Email subject line'),
    html: z.string().optional().describe('HTML content of the email'),
    text: z.string().optional().describe('Plain text content of the email'),
    cc: z.array(z.string().email()).optional().describe('Array of CC recipient email addresses'),
    bcc: z.array(z.string().email()).optional().describe('Array of BCC recipient email addresses'),
    attachments: z.array(z.object({
      filename: z.string().describe('Name of the attachment file'),
      content: z.string().describe('Base64 encoded content of the attachment'),
      mimeType: z.string().describe('MIME type of the attachment'),
      size: z.number().optional().describe('Size of the attachment in bytes'),
    })).optional().describe('Array of email attachments'),
    threadId: z.string().optional().describe('Thread ID to reply to an existing conversation'),
  }).refine(data => data.html || data.text, {
    message: 'Either html or text content must be provided',
  }),
  handler: async (params: unknown, context: MCPContext): Promise<MCPResult> => {
    const validatedParams = params as z.infer<typeof sendEmailTool.inputSchema>;
    
    try {
      const result = await gmailService.sendEmail(context, validatedParams);
      
      return {
        success: true,
        data: result,
        metadata: {
          tool: 'gmail.send_email',
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to send email',
      };
    }
  },
  requiredScopes: ['https://mail.google.com/'],
  idempotent: false,
  safeMode: false,
  examples: [
    {
      name: 'Send simple email',
      description: 'Send a basic email with text content',
      parameters: {
        to: ['<EMAIL>'],
        subject: 'Hello from MCP',
        text: 'This is a test email sent through the MCP Gmail integration.',
      },
    },
    {
      name: 'Send HTML email with CC',
      description: 'Send an HTML email with CC recipients',
      parameters: {
        to: ['<EMAIL>'],
        cc: ['<EMAIL>'],
        subject: 'Project Update',
        html: '<h1>Project Status</h1><p>The project is <strong>on track</strong>.</p>',
      },
    },
  ],
};

// Search Messages Tool
export const searchMessagesTool: MCPTool = {
  name: 'search',
  description: 'Search Gmail messages using Gmail search syntax',
  inputSchema: z.object({
    query: z.string().min(1).describe('Gmail search query (supports Gmail search operators)'),
    maxResults: z.number().min(1).max(500).default(10).describe('Maximum number of results to return'),
    pageToken: z.string().optional().describe('Token for pagination to get next page of results'),
  }),
  handler: async (params: unknown, context: MCPContext): Promise<MCPResult> => {
    const validatedParams = params as z.infer<typeof searchMessagesTool.inputSchema>;
    
    try {
      const result = await gmailService.searchMessages(context, validatedParams);
      
      return {
        success: true,
        data: result,
        metadata: {
          tool: 'gmail.search',
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to search messages',
      };
    }
  },
  requiredScopes: ['https://www.googleapis.com/auth/gmail.readonly'],
  idempotent: true,
  safeMode: true,
  examples: [
    {
      name: 'Search by sender',
      description: 'Find emails from a specific sender',
      parameters: {
        query: 'from:<EMAIL>',
        maxResults: 20,
      },
    },
    {
      name: 'Search unread emails',
      description: 'Find all unread emails',
      parameters: {
        query: 'is:unread',
        maxResults: 50,
      },
    },
  ],
};

// Get Message Tool
export const getMessageTool: MCPTool = {
  name: 'get_message',
  description: 'Retrieve a specific Gmail message by ID with configurable detail level',
  inputSchema: z.object({
    id: z.string().min(1).describe('Gmail message ID'),
    format: z.enum(['minimal', 'full', 'raw', 'metadata']).default('full').describe('Level of detail to retrieve'),
  }),
  handler: async (params: unknown, context: MCPContext): Promise<MCPResult> => {
    const validatedParams = params as z.infer<typeof getMessageTool.inputSchema>;
    
    try {
      const result = await gmailService.getMessage(context, validatedParams);
      
      return {
        success: true,
        data: result,
        metadata: {
          tool: 'gmail.get_message',
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get message',
      };
    }
  },
  requiredScopes: ['https://www.googleapis.com/auth/gmail.readonly'],
  idempotent: true,
  safeMode: true,
  examples: [
    {
      name: 'Get full message',
      description: 'Retrieve complete message details',
      parameters: {
        id: '18b2e1a2f3c4d5e6',
        format: 'full',
      },
    },
    {
      name: 'Get message metadata only',
      description: 'Retrieve just the message headers and metadata',
      parameters: {
        id: '18b2e1a2f3c4d5e6',
        format: 'metadata',
      },
    },
  ],
};

// Modify Labels Tool
export const modifyLabelsTool: MCPTool = {
  name: 'modify_labels',
  description: 'Add or remove labels from a Gmail message',
  inputSchema: z.object({
    messageId: z.string().min(1).describe('Gmail message ID to modify'),
    addLabelIds: z.array(z.string()).optional().describe('Array of label IDs to add to the message'),
    removeLabelIds: z.array(z.string()).optional().describe('Array of label IDs to remove from the message'),
  }).refine(data => data.addLabelIds || data.removeLabelIds, {
    message: 'At least one of addLabelIds or removeLabelIds must be provided',
  }),
  handler: async (params: unknown, context: MCPContext): Promise<MCPResult> => {
    const validatedParams = params as z.infer<typeof modifyLabelsTool.inputSchema>;
    
    try {
      const result = await gmailService.modifyLabels(context, validatedParams);
      
      return {
        success: true,
        data: result,
        metadata: {
          tool: 'gmail.modify_labels',
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to modify labels',
      };
    }
  },
  requiredScopes: ['https://mail.google.com/'],
  idempotent: false,
  safeMode: false,
  examples: [
    {
      name: 'Mark as important',
      description: 'Add the important label to a message',
      parameters: {
        messageId: '18b2e1a2f3c4d5e6',
        addLabelIds: ['IMPORTANT'],
      },
    },
    {
      name: 'Archive message',
      description: 'Remove inbox label to archive a message',
      parameters: {
        messageId: '18b2e1a2f3c4d5e6',
        removeLabelIds: ['INBOX'],
      },
    },
  ],
};

// List Threads Tool
export const listThreadsTool: MCPTool = {
  name: 'list_threads',
  description: 'List Gmail conversation threads with optional search filtering',
  inputSchema: z.object({
    query: z.string().optional().describe('Gmail search query to filter threads'),
    maxResults: z.number().min(1).max(500).default(10).describe('Maximum number of threads to return'),
    pageToken: z.string().optional().describe('Token for pagination to get next page of results'),
  }),
  handler: async (params: unknown, context: MCPContext): Promise<MCPResult> => {
    const validatedParams = params as z.infer<typeof listThreadsTool.inputSchema>;
    
    try {
      const result = await gmailService.listThreads(context, validatedParams);
      
      return {
        success: true,
        data: result,
        metadata: {
          tool: 'gmail.list_threads',
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to list threads',
      };
    }
  },
  requiredScopes: ['https://www.googleapis.com/auth/gmail.readonly'],
  idempotent: true,
  safeMode: true,
  examples: [
    {
      name: 'List recent threads',
      description: 'Get the most recent conversation threads',
      parameters: {
        maxResults: 25,
      },
    },
    {
      name: 'List threads from specific sender',
      description: 'Get conversation threads from a specific sender',
      parameters: {
        query: 'from:<EMAIL>',
        maxResults: 15,
      },
    },
  ],
};

// Watch Messages Tool
export const watchMessagesTool: MCPTool = {
  name: 'watch_start',
  description: 'Start watching for Gmail message changes via push notifications',
  inputSchema: z.object({
    topicName: z.string().min(1).describe('Google Cloud Pub/Sub topic name for notifications'),
    labelIds: z.array(z.string()).optional().describe('Array of label IDs to watch for changes'),
    labelFilterAction: z.enum(['include', 'exclude']).optional().describe('Whether to include or exclude the specified labels'),
  }),
  handler: async (params: unknown, context: MCPContext): Promise<MCPResult> => {
    const validatedParams = params as z.infer<typeof watchMessagesTool.inputSchema>;
    
    try {
      const result = await gmailService.watchMessages(context, validatedParams);
      
      return {
        success: true,
        data: result,
        metadata: {
          tool: 'gmail.watch_start',
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to start watching messages',
      };
    }
  },
  requiredScopes: ['https://mail.google.com/'],
  idempotent: false,
  safeMode: false,
  examples: [
    {
      name: 'Watch all messages',
      description: 'Start watching for changes to all messages',
      parameters: {
        topicName: 'projects/my-project/topics/gmail-notifications',
      },
    },
    {
      name: 'Watch inbox only',
      description: 'Watch for changes only to inbox messages',
      parameters: {
        topicName: 'projects/my-project/topics/gmail-notifications',
        labelIds: ['INBOX'],
        labelFilterAction: 'include',
      },
    },
  ],
};

// Stop Watch Tool
export const stopWatchTool: MCPTool = {
  name: 'watch_stop',
  description: 'Stop watching for Gmail message changes',
  inputSchema: z.object({}),
  handler: async (params: unknown, context: MCPContext): Promise<MCPResult> => {
    try {
      await gmailService.stopWatch(context);
      
      return {
        success: true,
        data: { message: 'Watch stopped successfully' },
        metadata: {
          tool: 'gmail.watch_stop',
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to stop watching messages',
      };
    }
  },
  requiredScopes: ['https://mail.google.com/'],
  idempotent: true,
  safeMode: true,
  examples: [
    {
      name: 'Stop watching',
      description: 'Stop all Gmail push notifications',
      parameters: {},
    },
  ],
};

// Export all Gmail tools
export const gmailTools = [
  sendEmailTool,
  searchMessagesTool,
  getMessageTool,
  modifyLabelsTool,
  listThreadsTool,
  watchMessagesTool,
  stopWatchTool,
];
