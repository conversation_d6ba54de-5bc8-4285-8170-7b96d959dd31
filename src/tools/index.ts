import { ToolRegistry } from '@/mcp/tool-registry';
import { logger } from '@/utils/logger';

// Import tool collections
import { gmailTools } from './gmail/tools';

export class ToolLoader {
  private static instance: ToolLoader;
  private registry = ToolRegistry.getInstance();

  private constructor() {}

  public static getInstance(): ToolLoader {
    if (!ToolLoader.instance) {
      ToolLoader.instance = new ToolLoader();
    }
    return ToolLoader.instance;
  }

  public loadAllTools(): void {
    logger.info('Loading all tools...');

    // Load Gmail tools
    this.loadGmailTools();

    // TODO: Load other tool collections
    // this.loadDriveTools();
    // this.loadDocsTools();
    // this.loadSheetsTools();
    // this.loadSlidesTools();
    // this.loadFormsTools();
    // this.loadCalendarTools();
    // this.loadContactsTools();
    // this.loadTasksTools();
    // this.loadKeepTools();
    // this.loadOpsTools();

    const stats = this.registry.getToolStats();
    logger.info('All tools loaded successfully', stats);
  }

  private loadGmailTools(): void {
    logger.info('Loading Gmail tools...');
    
    for (const tool of gmailTools) {
      this.registry.registerTool('gmail', tool, '1.0.0');
    }
    
    logger.info(`Loaded ${gmailTools.length} Gmail tools`);
  }

  // TODO: Implement other tool loaders
  /*
  private loadDriveTools(): void {
    logger.info('Loading Drive tools...');
    
    for (const tool of driveTools) {
      this.registry.registerTool('drive', tool, '1.0.0');
    }
    
    logger.info(`Loaded ${driveTools.length} Drive tools`);
  }

  private loadDocsTools(): void {
    logger.info('Loading Docs tools...');
    
    for (const tool of docsTools) {
      this.registry.registerTool('docs', tool, '1.0.0');
    }
    
    logger.info(`Loaded ${docsTools.length} Docs tools`);
  }

  private loadSheetsTools(): void {
    logger.info('Loading Sheets tools...');
    
    for (const tool of sheetsTools) {
      this.registry.registerTool('sheets', tool, '1.0.0');
    }
    
    logger.info(`Loaded ${sheetsTools.length} Sheets tools`);
  }

  private loadSlidesTools(): void {
    logger.info('Loading Slides tools...');
    
    for (const tool of slidesTools) {
      this.registry.registerTool('slides', tool, '1.0.0');
    }
    
    logger.info(`Loaded ${slidesTools.length} Slides tools`);
  }

  private loadFormsTools(): void {
    logger.info('Loading Forms tools...');
    
    for (const tool of formsTools) {
      this.registry.registerTool('forms', tool, '1.0.0');
    }
    
    logger.info(`Loaded ${formsTools.length} Forms tools`);
  }

  private loadCalendarTools(): void {
    logger.info('Loading Calendar tools...');
    
    for (const tool of calendarTools) {
      this.registry.registerTool('calendar', tool, '1.0.0');
    }
    
    logger.info(`Loaded ${calendarTools.length} Calendar tools`);
  }

  private loadContactsTools(): void {
    logger.info('Loading Contacts tools...');
    
    for (const tool of contactsTools) {
      this.registry.registerTool('contacts', tool, '1.0.0');
    }
    
    logger.info(`Loaded ${contactsTools.length} Contacts tools`);
  }

  private loadTasksTools(): void {
    logger.info('Loading Tasks tools...');
    
    for (const tool of tasksTools) {
      this.registry.registerTool('tasks', tool, '1.0.0');
    }
    
    logger.info(`Loaded ${tasksTools.length} Tasks tools`);
  }

  private loadKeepTools(): void {
    logger.info('Loading Keep tools...');
    
    for (const tool of keepTools) {
      this.registry.registerTool('keep', tool, '1.0.0');
    }
    
    logger.info(`Loaded ${keepTools.length} Keep tools`);
  }

  private loadOpsTools(): void {
    logger.info('Loading Ops tools...');
    
    for (const tool of opsTools) {
      this.registry.registerTool('ops', tool, '1.0.0');
    }
    
    logger.info(`Loaded ${opsTools.length} Ops tools`);
  }
  */

  public getLoadedToolsCount(): number {
    return this.registry.getToolStats().totalTools;
  }

  public getToolsByNamespace(namespace: string): number {
    const stats = this.registry.getToolStats();
    return stats.toolsByNamespace[namespace] || 0;
  }
}
