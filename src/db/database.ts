import Database from 'better-sqlite3';
import { readFileSync } from 'fs';
import { join } from 'path';
import { logger } from '@/utils/logger';
import type { User, Resource, AuditLog, OAuthTokens } from '@/types';

export class DatabaseManager {
  private db: Database.Database;
  private static instance: DatabaseManager;

  private constructor(dbPath: string) {
    this.db = new Database(dbPath);
    this.db.pragma('journal_mode = WAL');
    this.db.pragma('synchronous = NORMAL');
    this.db.pragma('cache_size = 1000');
    this.db.pragma('temp_store = memory');
    this.db.pragma('mmap_size = 268435456'); // 256MB
    this.initializeSchema();
  }

  public static getInstance(dbPath: string = './data/workspace.db'): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager(dbPath);
    }
    return DatabaseManager.instance;
  }

  private initializeSchema(): void {
    try {
      const schemaPath = join(__dirname, 'schema.sql');
      const schema = readFileSync(schemaPath, 'utf-8');
      this.db.exec(schema);
      logger.info('Database schema initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize database schema:', error);
      throw error;
    }
  }

  public getDatabase(): Database.Database {
    return this.db;
  }

  public close(): void {
    this.db.close();
  }

  // Transaction helper
  public transaction<T>(fn: (db: Database.Database) => T): T {
    const transaction = this.db.transaction(fn);
    return transaction(this.db);
  }

  // Health check
  public healthCheck(): boolean {
    try {
      const result = this.db.prepare('SELECT 1 as health').get() as { health: number };
      return result.health === 1;
    } catch {
      return false;
    }
  }

  // Cleanup expired records
  public cleanup(): void {
    const now = Date.now();
    
    // Clean expired sessions
    const cleanSessions = this.db.prepare(`
      DELETE FROM sessions WHERE expires_at < ?
    `);
    const sessionsDeleted = cleanSessions.run(now).changes;
    
    // Clean expired tokens
    const cleanTokens = this.db.prepare(`
      DELETE FROM oauth_tokens WHERE expires_at < ?
    `);
    const tokensDeleted = cleanTokens.run(now).changes;
    
    // Clean old rate limit records (older than 24 hours)
    const cleanRateLimits = this.db.prepare(`
      DELETE FROM rate_limits WHERE window_start < ?
    `);
    const rateLimitsDeleted = cleanRateLimits.run(now - 24 * 60 * 60 * 1000).changes;
    
    logger.info('Database cleanup completed', {
      sessionsDeleted,
      tokensDeleted,
      rateLimitsDeleted,
    });
  }

  // Backup database
  public backup(backupPath: string): void {
    try {
      this.db.backup(backupPath);
      logger.info(`Database backed up to ${backupPath}`);
    } catch (error) {
      logger.error('Database backup failed:', error);
      throw error;
    }
  }

  // Get database statistics
  public getStats(): Record<string, number> {
    const tables = [
      'users',
      'oauth_tokens',
      'resources',
      'sessions',
      'audit_logs',
      'rate_limits',
      'resource_aliases',
    ];

    const stats: Record<string, number> = {};
    
    for (const table of tables) {
      try {
        const result = this.db.prepare(`SELECT COUNT(*) as count FROM ${table}`).get() as {
          count: number;
        };
        stats[table] = result.count;
      } catch (error) {
        logger.warn(`Failed to get count for table ${table}:`, error);
        stats[table] = -1;
      }
    }

    return stats;
  }
}
