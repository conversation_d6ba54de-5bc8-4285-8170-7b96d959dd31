import { v4 as uuidv4 } from 'uuid';
import { BaseRepository } from './base';
import { EncryptionService } from '@/auth/encryption';
import type { User, UserRole, OAuthTokens } from '@/types';

interface UserRow {
  id: string;
  email: string;
  name: string;
  role: string;
  domains: string;
  scopes: string;
  created_at: string;
  updated_at: string;
  last_login?: string;
}

interface TokenRow {
  id: string;
  user_id: string;
  encrypted_tokens: string;
  scope: string;
  expires_at: number;
  created_at: string;
  updated_at: string;
}

export class UserRepository extends BaseRepository<User> {
  private encryption?: EncryptionService;

  constructor() {
    super('users');
  }

  private getEncryption(): EncryptionService {
    if (!this.encryption) {
      this.encryption = EncryptionService.getInstance();
    }
    return this.encryption;
  }

  protected mapRowToEntity(row: Record<string, unknown>): User {
    const userRow = row as UserRow;
    return {
      id: userRow.id,
      email: userRow.email,
      name: userRow.name,
      role: userRow.role as UserRole,
      domains: JSON.parse(userRow.domains) as string[],
      scopes: JSON.parse(userRow.scopes) as string[],
      created_at: userRow.created_at,
      updated_at: userRow.updated_at,
      last_login: userRow.last_login,
    };
  }

  protected mapEntityToRow(entity: Partial<User>): Record<string, unknown> {
    const row: Record<string, unknown> = {};
    
    if (entity.id) row.id = entity.id;
    if (entity.email) row.email = entity.email;
    if (entity.name) row.name = entity.name;
    if (entity.role) row.role = entity.role;
    if (entity.domains) row.domains = JSON.stringify(entity.domains);
    if (entity.scopes) row.scopes = JSON.stringify(entity.scopes);
    if (entity.last_login) row.last_login = entity.last_login;

    return row;
  }

  public async create(userData: Omit<User, 'id' | 'created_at' | 'updated_at'>): Promise<User> {
    const userWithId = {
      id: uuidv4(),
      ...userData,
    };
    return super.create(userWithId);
  }

  public async findByEmail(email: string): Promise<User | null> {
    return this.findOneBy('email', email);
  }

  public async findByRole(role: UserRole): Promise<User[]> {
    return this.findBy('role', role);
  }

  public async updateLastLogin(userId: string): Promise<User | null> {
    return this.update(userId, { last_login: new Date().toISOString() } as Partial<User>);
  }

  public async getUsersInDomain(domain: string): Promise<User[]> {
    try {
      const stmt = this.db.prepare(`
        SELECT * FROM ${this.tableName} 
        WHERE domains LIKE ?
      `);
      const rows = stmt.all(`%"${domain}"%`) as UserRow[];
      return rows
        .map(row => this.mapRowToEntity(row))
        .filter(user => user.domains.includes(domain));
    } catch (error) {
      throw error;
    }
  }

  // Token management methods
  public async storeTokens(userId: string, tokens: OAuthTokens): Promise<void> {
    try {
      const encryptedTokens = this.getEncryption().encryptObject(tokens);
      const tokenId = uuidv4();

      // Delete existing tokens for user
      const deleteStmt = this.db.prepare('DELETE FROM oauth_tokens WHERE user_id = ?');
      deleteStmt.run(userId);

      // Insert new tokens
      const insertStmt = this.db.prepare(`
        INSERT INTO oauth_tokens (id, user_id, encrypted_tokens, scope, expires_at)
        VALUES (?, ?, ?, ?, ?)
      `);
      
      insertStmt.run(
        tokenId,
        userId,
        encryptedTokens,
        tokens.scope,
        tokens.expires_at
      );
    } catch (error) {
      throw error;
    }
  }

  public async getTokens(userId: string): Promise<OAuthTokens | null> {
    try {
      const stmt = this.db.prepare(`
        SELECT encrypted_tokens, expires_at 
        FROM oauth_tokens 
        WHERE user_id = ? AND expires_at > ?
      `);
      
      const row = stmt.get(userId, Date.now()) as TokenRow | undefined;
      
      if (!row) {
        return null;
      }

      return this.getEncryption().decryptObject<OAuthTokens>(row.encrypted_tokens);
    } catch (error) {
      throw error;
    }
  }

  public async updateTokens(userId: string, tokens: Partial<OAuthTokens>): Promise<void> {
    try {
      const existingTokens = await this.getTokens(userId);
      if (!existingTokens) {
        throw new Error('No existing tokens found for user');
      }

      const updatedTokens = { ...existingTokens, ...tokens };
      await this.storeTokens(userId, updatedTokens);
    } catch (error) {
      throw error;
    }
  }

  public async revokeTokens(userId: string): Promise<void> {
    try {
      const stmt = this.db.prepare('DELETE FROM oauth_tokens WHERE user_id = ?');
      stmt.run(userId);
    } catch (error) {
      throw error;
    }
  }

  public async getExpiredTokens(): Promise<Array<{ userId: string; tokens: OAuthTokens }>> {
    try {
      const stmt = this.db.prepare(`
        SELECT user_id, encrypted_tokens 
        FROM oauth_tokens 
        WHERE expires_at <= ?
      `);
      
      const rows = stmt.all(Date.now()) as TokenRow[];
      
      return rows.map(row => ({
        userId: row.user_id,
        tokens: this.getEncryption().decryptObject<OAuthTokens>(row.encrypted_tokens),
      }));
    } catch (error) {
      throw error;
    }
  }

  public async cleanupExpiredTokens(): Promise<number> {
    try {
      const stmt = this.db.prepare('DELETE FROM oauth_tokens WHERE expires_at <= ?');
      const result = stmt.run(Date.now());
      return result.changes;
    } catch (error) {
      throw error;
    }
  }

  public async hasValidTokens(userId: string): Promise<boolean> {
    try {
      const stmt = this.db.prepare(`
        SELECT 1 FROM oauth_tokens 
        WHERE user_id = ? AND expires_at > ?
        LIMIT 1
      `);
      
      const result = stmt.get(userId, Date.now());
      return !!result;
    } catch (error) {
      return false;
    }
  }
}
