import { v4 as uuidv4 } from 'uuid';
import { BaseRepository } from './base';
import type { AuditLog, ResourceType } from '@/types';

interface AuditLogRow {
  id: string;
  user_id: string;
  tool_name: string;
  resource_type?: string;
  resource_id?: string;
  action: string;
  parameters?: string;
  result?: 'success' | 'error';
  error_message?: string;
  ip_address?: string;
  user_agent?: string;
  request_id: string;
  created_at: string;
}

export class AuditRepository extends BaseRepository<AuditLog> {
  constructor() {
    super('audit_logs');
  }

  protected mapRowToEntity(row: Record<string, unknown>): AuditLog {
    const auditRow = row as AuditLogRow;
    return {
      id: auditRow.id,
      user_id: auditRow.user_id,
      tool_name: auditRow.tool_name,
      resource_type: auditRow.resource_type as ResourceType | undefined,
      resource_id: auditRow.resource_id,
      action: auditRow.action,
      parameters: auditRow.parameters ? JSON.parse(auditRow.parameters) : undefined,
      result: auditRow.result,
      error_message: auditRow.error_message,
      ip_address: auditRow.ip_address,
      user_agent: auditRow.user_agent,
      request_id: auditRow.request_id,
      created_at: auditRow.created_at,
    };
  }

  protected mapEntityToRow(entity: Partial<AuditLog>): Record<string, unknown> {
    const row: Record<string, unknown> = {};
    
    if (entity.id) row.id = entity.id;
    if (entity.user_id) row.user_id = entity.user_id;
    if (entity.tool_name) row.tool_name = entity.tool_name;
    if (entity.resource_type) row.resource_type = entity.resource_type;
    if (entity.resource_id) row.resource_id = entity.resource_id;
    if (entity.action) row.action = entity.action;
    if (entity.parameters) row.parameters = JSON.stringify(entity.parameters);
    if (entity.result) row.result = entity.result;
    if (entity.error_message) row.error_message = entity.error_message;
    if (entity.ip_address) row.ip_address = entity.ip_address;
    if (entity.user_agent) row.user_agent = entity.user_agent;
    if (entity.request_id) row.request_id = entity.request_id;

    return row;
  }

  public async create(auditData: Omit<AuditLog, 'id' | 'created_at'>): Promise<AuditLog> {
    const auditWithId = {
      id: uuidv4(),
      ...auditData,
    };
    return super.create(auditWithId);
  }

  public async logToolExecution(
    userId: string,
    toolName: string,
    action: string,
    requestId: string,
    options: {
      resourceType?: ResourceType;
      resourceId?: string;
      parameters?: Record<string, unknown>;
      result?: 'success' | 'error';
      errorMessage?: string;
      ipAddress?: string;
      userAgent?: string;
    } = {}
  ): Promise<AuditLog> {
    return this.create({
      user_id: userId,
      tool_name: toolName,
      resource_type: options.resourceType,
      resource_id: options.resourceId,
      action,
      parameters: options.parameters,
      result: options.result,
      error_message: options.errorMessage,
      ip_address: options.ipAddress,
      user_agent: options.userAgent,
      request_id: requestId,
    });
  }

  public async findByUser(
    userId: string,
    limit: number = 100,
    offset: number = 0
  ): Promise<AuditLog[]> {
    try {
      const stmt = this.db.prepare(`
        SELECT * FROM ${this.tableName}
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `);
      
      const rows = stmt.all(userId, limit, offset) as AuditLogRow[];
      return rows.map(row => this.mapRowToEntity(row));
    } catch (error) {
      throw error;
    }
  }

  public async findByTool(
    toolName: string,
    limit: number = 100,
    offset: number = 0
  ): Promise<AuditLog[]> {
    try {
      const stmt = this.db.prepare(`
        SELECT * FROM ${this.tableName}
        WHERE tool_name = ?
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `);
      
      const rows = stmt.all(toolName, limit, offset) as AuditLogRow[];
      return rows.map(row => this.mapRowToEntity(row));
    } catch (error) {
      throw error;
    }
  }

  public async findByResource(
    resourceId: string,
    limit: number = 100,
    offset: number = 0
  ): Promise<AuditLog[]> {
    try {
      const stmt = this.db.prepare(`
        SELECT * FROM ${this.tableName}
        WHERE resource_id = ?
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `);
      
      const rows = stmt.all(resourceId, limit, offset) as AuditLogRow[];
      return rows.map(row => this.mapRowToEntity(row));
    } catch (error) {
      throw error;
    }
  }

  public async findByDateRange(
    startDate: string,
    endDate: string,
    userId?: string,
    limit: number = 1000
  ): Promise<AuditLog[]> {
    try {
      let query = `
        SELECT * FROM ${this.tableName}
        WHERE created_at >= ? AND created_at <= ?
      `;
      const params: unknown[] = [startDate, endDate];

      if (userId) {
        query += ' AND user_id = ?';
        params.push(userId);
      }

      query += ' ORDER BY created_at DESC LIMIT ?';
      params.push(limit);

      const stmt = this.db.prepare(query);
      const rows = stmt.all(...params) as AuditLogRow[];
      return rows.map(row => this.mapRowToEntity(row));
    } catch (error) {
      throw error;
    }
  }

  public async getErrorLogs(
    limit: number = 100,
    userId?: string
  ): Promise<AuditLog[]> {
    try {
      let query = `
        SELECT * FROM ${this.tableName}
        WHERE result = 'error'
      `;
      const params: unknown[] = [];

      if (userId) {
        query += ' AND user_id = ?';
        params.push(userId);
      }

      query += ' ORDER BY created_at DESC LIMIT ?';
      params.push(limit);

      const stmt = this.db.prepare(query);
      const rows = stmt.all(...params) as AuditLogRow[];
      return rows.map(row => this.mapRowToEntity(row));
    } catch (error) {
      throw error;
    }
  }

  public async getToolUsageStats(
    startDate?: string,
    endDate?: string,
    userId?: string
  ): Promise<Array<{ tool_name: string; usage_count: number; success_count: number; error_count: number }>> {
    try {
      let query = `
        SELECT 
          tool_name,
          COUNT(*) as usage_count,
          SUM(CASE WHEN result = 'success' THEN 1 ELSE 0 END) as success_count,
          SUM(CASE WHEN result = 'error' THEN 1 ELSE 0 END) as error_count
        FROM ${this.tableName}
        WHERE 1=1
      `;
      const params: unknown[] = [];

      if (startDate) {
        query += ' AND created_at >= ?';
        params.push(startDate);
      }

      if (endDate) {
        query += ' AND created_at <= ?';
        params.push(endDate);
      }

      if (userId) {
        query += ' AND user_id = ?';
        params.push(userId);
      }

      query += ' GROUP BY tool_name ORDER BY usage_count DESC';

      const stmt = this.db.prepare(query);
      return stmt.all(...params) as Array<{
        tool_name: string;
        usage_count: number;
        success_count: number;
        error_count: number;
      }>;
    } catch (error) {
      throw error;
    }
  }

  public async getUserActivityStats(
    startDate?: string,
    endDate?: string
  ): Promise<Array<{ user_id: string; activity_count: number; last_activity: string }>> {
    try {
      let query = `
        SELECT 
          user_id,
          COUNT(*) as activity_count,
          MAX(created_at) as last_activity
        FROM ${this.tableName}
        WHERE 1=1
      `;
      const params: unknown[] = [];

      if (startDate) {
        query += ' AND created_at >= ?';
        params.push(startDate);
      }

      if (endDate) {
        query += ' AND created_at <= ?';
        params.push(endDate);
      }

      query += ' GROUP BY user_id ORDER BY activity_count DESC';

      const stmt = this.db.prepare(query);
      return stmt.all(...params) as Array<{
        user_id: string;
        activity_count: number;
        last_activity: string;
      }>;
    } catch (error) {
      throw error;
    }
  }

  public async cleanupOldLogs(daysToKeep: number = 90): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      
      const stmt = this.db.prepare(`
        DELETE FROM ${this.tableName}
        WHERE created_at < ?
      `);
      
      const result = stmt.run(cutoffDate.toISOString());
      return result.changes;
    } catch (error) {
      throw error;
    }
  }
}
