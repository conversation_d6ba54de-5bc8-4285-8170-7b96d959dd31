import { v4 as uuidv4 } from 'uuid';
import { BaseRepository } from './base';
import type { Resource, ResourceType } from '@/types';

interface ResourceRow {
  id: string;
  type: string;
  app: string;
  title: string;
  external_id: string;
  url?: string;
  created_by: string;
  linked_to?: string;
  metadata?: string;
  created_at: string;
  updated_at: string;
}

interface ResourceAliasRow {
  id: string;
  resource_id: string;
  alias: string;
  user_id: string;
  created_at: string;
}

export class ResourceRepository extends BaseRepository<Resource> {
  constructor() {
    super('resources');
  }

  protected mapRowToEntity(row: Record<string, unknown>): Resource {
    const resourceRow = row as ResourceRow;
    return {
      id: resourceRow.id,
      type: resourceRow.type as ResourceType,
      app: resourceRow.app,
      title: resourceRow.title,
      external_id: resourceRow.external_id,
      url: resourceRow.url,
      created_by: resourceRow.created_by,
      linked_to: resourceRow.linked_to ? JSON.parse(resourceRow.linked_to) : undefined,
      metadata: resourceRow.metadata ? JSON.parse(resourceRow.metadata) : undefined,
      created_at: resourceRow.created_at,
      updated_at: resourceRow.updated_at,
    };
  }

  protected mapEntityToRow(entity: Partial<Resource>): Record<string, unknown> {
    const row: Record<string, unknown> = {};
    
    if (entity.id) row.id = entity.id;
    if (entity.type) row.type = entity.type;
    if (entity.app) row.app = entity.app;
    if (entity.title) row.title = entity.title;
    if (entity.external_id) row.external_id = entity.external_id;
    if (entity.url) row.url = entity.url;
    if (entity.created_by) row.created_by = entity.created_by;
    if (entity.linked_to) row.linked_to = JSON.stringify(entity.linked_to);
    if (entity.metadata) row.metadata = JSON.stringify(entity.metadata);

    return row;
  }

  public async create(resourceData: Omit<Resource, 'id' | 'created_at' | 'updated_at'>): Promise<Resource> {
    const resourceWithId = {
      id: uuidv4(),
      ...resourceData,
    };
    return super.create(resourceWithId);
  }

  public async findByType(type: ResourceType, userId?: string): Promise<Resource[]> {
    try {
      let query = `SELECT * FROM ${this.tableName} WHERE type = ?`;
      const params: unknown[] = [type];

      if (userId) {
        query += ' AND created_by = ?';
        params.push(userId);
      }

      query += ' ORDER BY created_at DESC';

      const stmt = this.db.prepare(query);
      const rows = stmt.all(...params) as ResourceRow[];
      return rows.map(row => this.mapRowToEntity(row));
    } catch (error) {
      throw error;
    }
  }

  public async findByApp(app: string, userId?: string): Promise<Resource[]> {
    try {
      let query = `SELECT * FROM ${this.tableName} WHERE app = ?`;
      const params: unknown[] = [app];

      if (userId) {
        query += ' AND created_by = ?';
        params.push(userId);
      }

      query += ' ORDER BY created_at DESC';

      const stmt = this.db.prepare(query);
      const rows = stmt.all(...params) as ResourceRow[];
      return rows.map(row => this.mapRowToEntity(row));
    } catch (error) {
      throw error;
    }
  }

  public async findByExternalId(externalId: string, type?: ResourceType): Promise<Resource | null> {
    try {
      let query = `SELECT * FROM ${this.tableName} WHERE external_id = ?`;
      const params: unknown[] = [externalId];

      if (type) {
        query += ' AND type = ?';
        params.push(type);
      }

      query += ' LIMIT 1';

      const stmt = this.db.prepare(query);
      const row = stmt.get(...params) as ResourceRow | undefined;
      return row ? this.mapRowToEntity(row) : null;
    } catch (error) {
      throw error;
    }
  }

  public async findByUser(userId: string, limit?: number): Promise<Resource[]> {
    try {
      let query = `SELECT * FROM ${this.tableName} WHERE created_by = ? ORDER BY created_at DESC`;
      const params: unknown[] = [userId];

      if (limit) {
        query += ' LIMIT ?';
        params.push(limit);
      }

      const stmt = this.db.prepare(query);
      const rows = stmt.all(...params) as ResourceRow[];
      return rows.map(row => this.mapRowToEntity(row));
    } catch (error) {
      throw error;
    }
  }

  public async search(query: string, userId?: string, type?: ResourceType): Promise<Resource[]> {
    try {
      let sql = `
        SELECT * FROM ${this.tableName} 
        WHERE (title LIKE ? OR external_id LIKE ?)
      `;
      const params: unknown[] = [`%${query}%`, `%${query}%`];

      if (userId) {
        sql += ' AND created_by = ?';
        params.push(userId);
      }

      if (type) {
        sql += ' AND type = ?';
        params.push(type);
      }

      sql += ' ORDER BY created_at DESC LIMIT 50';

      const stmt = this.db.prepare(sql);
      const rows = stmt.all(...params) as ResourceRow[];
      return rows.map(row => this.mapRowToEntity(row));
    } catch (error) {
      throw error;
    }
  }

  public async linkResources(resourceId: string, linkedResourceIds: string[]): Promise<Resource | null> {
    const resource = await this.findById(resourceId);
    if (!resource) {
      return null;
    }

    const existingLinks = resource.linked_to || [];
    const newLinks = [...new Set([...existingLinks, ...linkedResourceIds])];

    return this.update(resourceId, { linked_to: newLinks } as Partial<Resource>);
  }

  public async unlinkResources(resourceId: string, linkedResourceIds: string[]): Promise<Resource | null> {
    const resource = await this.findById(resourceId);
    if (!resource) {
      return null;
    }

    const existingLinks = resource.linked_to || [];
    const newLinks = existingLinks.filter(id => !linkedResourceIds.includes(id));

    return this.update(resourceId, { linked_to: newLinks } as Partial<Resource>);
  }

  public async getLinkedResources(resourceId: string): Promise<Resource[]> {
    const resource = await this.findById(resourceId);
    if (!resource || !resource.linked_to) {
      return [];
    }

    const linkedResources: Resource[] = [];
    for (const linkedId of resource.linked_to) {
      const linkedResource = await this.findById(linkedId);
      if (linkedResource) {
        linkedResources.push(linkedResource);
      }
    }

    return linkedResources;
  }

  // Alias management
  public async createAlias(resourceId: string, alias: string, userId: string): Promise<void> {
    try {
      const aliasId = uuidv4();
      const stmt = this.db.prepare(`
        INSERT INTO resource_aliases (id, resource_id, alias, user_id)
        VALUES (?, ?, ?, ?)
      `);
      stmt.run(aliasId, resourceId, alias, userId);
    } catch (error) {
      throw error;
    }
  }

  public async findByAlias(alias: string, userId: string): Promise<Resource | null> {
    try {
      const stmt = this.db.prepare(`
        SELECT r.* FROM ${this.tableName} r
        JOIN resource_aliases ra ON r.id = ra.resource_id
        WHERE ra.alias = ? AND ra.user_id = ?
        LIMIT 1
      `);
      
      const row = stmt.get(alias, userId) as ResourceRow | undefined;
      return row ? this.mapRowToEntity(row) : null;
    } catch (error) {
      throw error;
    }
  }

  public async getAliases(resourceId: string, userId: string): Promise<string[]> {
    try {
      const stmt = this.db.prepare(`
        SELECT alias FROM resource_aliases
        WHERE resource_id = ? AND user_id = ?
      `);
      
      const rows = stmt.all(resourceId, userId) as Array<{ alias: string }>;
      return rows.map(row => row.alias);
    } catch (error) {
      throw error;
    }
  }

  public async deleteAlias(alias: string, userId: string): Promise<boolean> {
    try {
      const stmt = this.db.prepare(`
        DELETE FROM resource_aliases
        WHERE alias = ? AND user_id = ?
      `);
      
      const result = stmt.run(alias, userId);
      return result.changes > 0;
    } catch (error) {
      throw error;
    }
  }

  public async getUserAliases(userId: string): Promise<Array<{ alias: string; resource: Resource }>> {
    try {
      const stmt = this.db.prepare(`
        SELECT ra.alias, r.* FROM resource_aliases ra
        JOIN ${this.tableName} r ON ra.resource_id = r.id
        WHERE ra.user_id = ?
        ORDER BY ra.created_at DESC
      `);
      
      const rows = stmt.all(userId) as Array<ResourceAliasRow & ResourceRow>;
      return rows.map(row => ({
        alias: row.alias,
        resource: this.mapRowToEntity(row),
      }));
    } catch (error) {
      throw error;
    }
  }
}
