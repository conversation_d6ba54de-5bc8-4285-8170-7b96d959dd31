import type Database from 'better-sqlite3';
import { DatabaseManager } from '@/db/database';
import { logger } from '@/utils/logger';

export abstract class BaseRepository<T> {
  protected db: Database.Database;
  protected tableName: string;

  constructor(tableName: string) {
    this.db = DatabaseManager.getInstance().getDatabase();
    this.tableName = tableName;
  }

  protected abstract mapRowToEntity(row: Record<string, unknown>): T;
  protected abstract mapEntityToRow(entity: Partial<T>): Record<string, unknown>;

  public async findById(id: string): Promise<T | null> {
    try {
      const stmt = this.db.prepare(`SELECT * FROM ${this.tableName} WHERE id = ?`);
      const row = stmt.get(id) as Record<string, unknown> | undefined;
      return row ? this.mapRowToEntity(row) : null;
    } catch (error) {
      logger.error(`Error finding ${this.tableName} by id ${id}:`, error);
      throw error;
    }
  }

  public async findAll(limit?: number, offset?: number): Promise<T[]> {
    try {
      let query = `SELECT * FROM ${this.tableName}`;
      const params: unknown[] = [];

      if (limit) {
        query += ' LIMIT ?';
        params.push(limit);
        
        if (offset) {
          query += ' OFFSET ?';
          params.push(offset);
        }
      }

      const stmt = this.db.prepare(query);
      const rows = stmt.all(...params) as Record<string, unknown>[];
      return rows.map(row => this.mapRowToEntity(row));
    } catch (error) {
      logger.error(`Error finding all ${this.tableName}:`, error);
      throw error;
    }
  }

  public async create(entity: Omit<T, 'id' | 'created_at' | 'updated_at'>): Promise<T> {
    try {
      const row = this.mapEntityToRow(entity);
      const columns = Object.keys(row);
      const placeholders = columns.map(() => '?').join(', ');
      const values = Object.values(row);

      const stmt = this.db.prepare(`
        INSERT INTO ${this.tableName} (${columns.join(', ')})
        VALUES (${placeholders})
      `);
      
      const result = stmt.run(...values);
      const created = await this.findById(result.lastInsertRowid as string);
      
      if (!created) {
        throw new Error(`Failed to create ${this.tableName}`);
      }
      
      return created;
    } catch (error) {
      logger.error(`Error creating ${this.tableName}:`, error);
      throw error;
    }
  }

  public async update(id: string, updates: Partial<T>): Promise<T | null> {
    try {
      const row = this.mapEntityToRow(updates);
      const columns = Object.keys(row);
      const setClause = columns.map(col => `${col} = ?`).join(', ');
      const values = [...Object.values(row), id];

      const stmt = this.db.prepare(`
        UPDATE ${this.tableName}
        SET ${setClause}
        WHERE id = ?
      `);
      
      const result = stmt.run(...values);
      
      if (result.changes === 0) {
        return null;
      }
      
      return await this.findById(id);
    } catch (error) {
      logger.error(`Error updating ${this.tableName} with id ${id}:`, error);
      throw error;
    }
  }

  public async delete(id: string): Promise<boolean> {
    try {
      const stmt = this.db.prepare(`DELETE FROM ${this.tableName} WHERE id = ?`);
      const result = stmt.run(id);
      return result.changes > 0;
    } catch (error) {
      logger.error(`Error deleting ${this.tableName} with id ${id}:`, error);
      throw error;
    }
  }

  public async count(whereClause?: string, params?: unknown[]): Promise<number> {
    try {
      let query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
      
      if (whereClause) {
        query += ` WHERE ${whereClause}`;
      }

      const stmt = this.db.prepare(query);
      const result = stmt.get(...(params || [])) as { count: number };
      return result.count;
    } catch (error) {
      logger.error(`Error counting ${this.tableName}:`, error);
      throw error;
    }
  }

  public async exists(id: string): Promise<boolean> {
    try {
      const stmt = this.db.prepare(`SELECT 1 FROM ${this.tableName} WHERE id = ? LIMIT 1`);
      const result = stmt.get(id);
      return !!result;
    } catch (error) {
      logger.error(`Error checking existence of ${this.tableName} with id ${id}:`, error);
      throw error;
    }
  }

  protected async findBy(
    column: string,
    value: unknown,
    limit?: number
  ): Promise<T[]> {
    try {
      let query = `SELECT * FROM ${this.tableName} WHERE ${column} = ?`;
      const params: unknown[] = [value];

      if (limit) {
        query += ' LIMIT ?';
        params.push(limit);
      }

      const stmt = this.db.prepare(query);
      const rows = stmt.all(...params) as Record<string, unknown>[];
      return rows.map(row => this.mapRowToEntity(row));
    } catch (error) {
      logger.error(`Error finding ${this.tableName} by ${column}:`, error);
      throw error;
    }
  }

  protected async findOneBy(column: string, value: unknown): Promise<T | null> {
    const results = await this.findBy(column, value, 1);
    return results.length > 0 ? results[0] : null;
  }
}
