import { z } from 'zod';

// MCP Protocol Types
export interface MCPTool {
  name: string;
  description: string;
  inputSchema: z.ZodSchema;
  handler: (params: unknown, context: MCPContext) => Promise<MCPResult>;
  requiredScopes: string[];
  idempotent?: boolean;
  safeMode?: boolean;
  examples?: MCPExample[];
}

export interface MCPExample {
  name: string;
  description: string;
  parameters: Record<string, unknown>;
  expectedResult?: unknown;
}

export interface MCPResult {
  success: boolean;
  data?: unknown;
  error?: string;
  metadata?: Record<string, unknown>;
}

export interface MCPContext {
  userId: string;
  userEmail: string;
  role: UserRole;
  scopes: string[];
  requestId: string;
  sessionId?: string;
}

// Authentication Types
export interface OAuthTokens {
  access_token: string;
  refresh_token: string;
  scope: string;
  token_type: string;
  expires_at: number;
}

export interface ServiceAccountConfig {
  type: string;
  project_id: string;
  private_key_id: string;
  private_key: string;
  client_email: string;
  client_id: string;
  auth_uri: string;
  token_uri: string;
  auth_provider_x509_cert_url: string;
  client_x509_cert_url: string;
}

// User & Role Types
export enum UserRole {
  ADMIN = 'admin',
  EDITOR = 'editor',
  VIEWER = 'viewer',
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  domains: string[];
  scopes: string[];
  created_at: string;
  updated_at: string;
  last_login?: string;
}

// Resource Types
export enum ResourceType {
  GMAIL_MESSAGE = 'gmail_message',
  GMAIL_THREAD = 'gmail_thread',
  DRIVE_FILE = 'drive_file',
  DRIVE_FOLDER = 'drive_folder',
  DOCS_DOCUMENT = 'docs_document',
  SHEETS_SPREADSHEET = 'sheets_spreadsheet',
  SLIDES_PRESENTATION = 'slides_presentation',
  FORMS_FORM = 'forms_form',
  CALENDAR_EVENT = 'calendar_event',
  CONTACTS_PERSON = 'contacts_person',
  TASKS_TASK = 'tasks_task',
  KEEP_NOTE = 'keep_note',
}

export interface Resource {
  id: string;
  type: ResourceType;
  app: string;
  title: string;
  external_id: string;
  url?: string;
  created_by: string;
  linked_to?: string[];
  metadata?: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

// Google API Types
export interface GoogleClientConfig {
  userId: string;
  scopes: string[];
  impersonateUser?: string;
}

// Rate Limiting Types
export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests: boolean;
  keyGenerator?: (userId: string, toolName: string) => string;
}

// Audit Types
export interface AuditLog {
  id: string;
  user_id: string;
  tool_name: string;
  resource_type?: ResourceType;
  resource_id?: string;
  action: string;
  parameters?: Record<string, unknown>;
  result?: 'success' | 'error';
  error_message?: string;
  ip_address?: string;
  user_agent?: string;
  request_id: string;
  created_at: string;
}

// Configuration Types
export interface ServerConfig {
  port: number;
  nodeEnv: string;
  logLevel: string;
  corsOrigins: string[];
  rateLimitConfig: RateLimitConfig;
  dbUrl: string;
  encryptionKey: string;
  jwtSecret: string;
  allowedDomains: string[];
  defaultScopes: string[];
}

export interface GoogleConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  serviceAccountPath?: string;
  impersonateUserEmail?: string;
  apiRetryAttempts: number;
  apiRetryDelay: number;
  apiTimeout: number;
}

// Error Types
export class MCPError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public details?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'MCPError';
  }
}

export class AuthenticationError extends MCPError {
  constructor(message: string, details?: Record<string, unknown>) {
    super(message, 'AUTHENTICATION_ERROR', 401, details);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends MCPError {
  constructor(message: string, details?: Record<string, unknown>) {
    super(message, 'AUTHORIZATION_ERROR', 403, details);
    this.name = 'AuthorizationError';
  }
}

export class ValidationError extends MCPError {
  constructor(message: string, details?: Record<string, unknown>) {
    super(message, 'VALIDATION_ERROR', 400, details);
    this.name = 'ValidationError';
  }
}

export class RateLimitError extends MCPError {
  constructor(message: string, details?: Record<string, unknown>) {
    super(message, 'RATE_LIMIT_ERROR', 429, details);
    this.name = 'RateLimitError';
  }
}

export class GoogleAPIError extends MCPError {
  constructor(message: string, details?: Record<string, unknown>) {
    super(message, 'GOOGLE_API_ERROR', 502, details);
    this.name = 'GoogleAPIError';
  }
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
