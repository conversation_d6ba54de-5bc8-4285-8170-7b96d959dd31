import { config } from 'dotenv';
import { existsSync } from 'fs';
import { logger } from './logger';
import type { ServerConfig, GoogleConfig, RateLimitConfig } from '@/types';

// Load environment variables
config();

export class ConfigManager {
  private static instance: ConfigManager;
  private serverConfig: ServerConfig;
  private googleConfig: GoogleConfig;
  private rateLimitConfig: RateLimitConfig;

  private constructor() {
    this.validateEnvironment();
    this.serverConfig = this.loadServerConfig();
    this.googleConfig = this.loadGoogleConfig();
    this.rateLimitConfig = this.loadRateLimitConfig();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  private validateEnvironment(): void {
    const required = [
      'GOOGLE_CLIENT_ID',
      'GOOGLE_CLIENT_SECRET',
      'OAUTH_REDIRECT_URI',
      'ENCRYPTION_KEY',
      'JWT_SECRET',
    ];

    const missing = required.filter(key => !process.env[key]);
    if (missing.length > 0) {
      throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }

    // Validate encryption key length
    const encryptionKey = process.env.ENCRYPTION_KEY;
    if (encryptionKey && encryptionKey.length < 32) {
      throw new Error('ENCRYPTION_KEY must be at least 32 characters long');
    }

    // Validate service account file if specified
    const serviceAccountPath = process.env.SERVICE_ACCOUNT_JSON_PATH;
    if (serviceAccountPath && !existsSync(serviceAccountPath)) {
      logger.warn(`Service account file not found: ${serviceAccountPath}`);
    }
  }

  private loadServerConfig(): ServerConfig {
    return {
      port: parseInt(process.env.PORT || '3000', 10),
      nodeEnv: process.env.NODE_ENV || 'development',
      logLevel: process.env.LOG_LEVEL || 'info',
      corsOrigins: (process.env.CORS_ORIGINS || 'http://localhost:3000').split(','),
      rateLimitConfig: this.loadRateLimitConfig(),
      dbUrl: process.env.DB_URL || './data/workspace.db',
      encryptionKey: process.env.ENCRYPTION_KEY!,
      jwtSecret: process.env.JWT_SECRET!,
      allowedDomains: (process.env.ALLOWED_DOMAINS || '').split(',').filter(Boolean),
      defaultScopes: (process.env.DEFAULT_SCOPES || '').split(',').filter(Boolean),
    };
  }

  private loadGoogleConfig(): GoogleConfig {
    return {
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      redirectUri: process.env.OAUTH_REDIRECT_URI!,
      serviceAccountPath: process.env.SERVICE_ACCOUNT_JSON_PATH,
      impersonateUserEmail: process.env.IMPERSONATE_USER_EMAIL,
      apiRetryAttempts: parseInt(process.env.GOOGLE_API_RETRY_ATTEMPTS || '3', 10),
      apiRetryDelay: parseInt(process.env.GOOGLE_API_RETRY_DELAY || '1000', 10),
      apiTimeout: parseInt(process.env.GOOGLE_API_TIMEOUT || '30000', 10),
    };
  }

  private loadRateLimitConfig(): RateLimitConfig {
    return {
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000', 10),
      maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
      skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS === 'true',
      keyGenerator: (userId: string, toolName: string) => `${userId}:${toolName}`,
    };
  }

  public getServerConfig(): ServerConfig {
    return { ...this.serverConfig };
  }

  public getGoogleConfig(): GoogleConfig {
    return { ...this.googleConfig };
  }

  public getRateLimitConfig(): RateLimitConfig {
    return { ...this.rateLimitConfig };
  }

  public isDevelopment(): boolean {
    return this.serverConfig.nodeEnv === 'development';
  }

  public isProduction(): boolean {
    return this.serverConfig.nodeEnv === 'production';
  }

  public getFeatureFlags(): {
    enablePushNotifications: boolean;
    enableCrossAppMacros: boolean;
    enableSafeMode: boolean;
    auditLogEnabled: boolean;
    requestLogEnabled: boolean;
  } {
    return {
      enablePushNotifications: process.env.ENABLE_PUSH_NOTIFICATIONS === 'true',
      enableCrossAppMacros: process.env.ENABLE_CROSS_APP_MACROS !== 'false', // Default true
      enableSafeMode: process.env.ENABLE_SAFE_MODE === 'true',
      auditLogEnabled: process.env.AUDIT_LOG_ENABLED !== 'false', // Default true
      requestLogEnabled: process.env.REQUEST_LOG_ENABLED !== 'false', // Default true
    };
  }

  public getWebhookConfig(): {
    baseUrl?: string;
    pubsubTopicName?: string;
  } {
    return {
      baseUrl: process.env.WEBHOOK_BASE_URL,
      pubsubTopicName: process.env.PUBSUB_TOPIC_NAME,
    };
  }

  public getDatabaseConfig(): {
    url: string;
    backupInterval: number;
  } {
    return {
      url: this.serverConfig.dbUrl,
      backupInterval: parseInt(process.env.DB_BACKUP_INTERVAL || '3600000', 10), // 1 hour
    };
  }

  public getAuditConfig(): {
    enabled: boolean;
    logPath?: string;
    retentionDays: number;
  } {
    return {
      enabled: this.getFeatureFlags().auditLogEnabled,
      logPath: process.env.AUDIT_LOG_PATH,
      retentionDays: parseInt(process.env.AUDIT_RETENTION_DAYS || '90', 10),
    };
  }

  public validateDomain(domain: string): boolean {
    const allowedDomains = this.serverConfig.allowedDomains;
    if (allowedDomains.length === 0) {
      return true; // No domain restrictions
    }
    return allowedDomains.includes(domain);
  }

  public getDefaultScopes(): string[] {
    return this.serverConfig.defaultScopes;
  }

  public getAllowedDomains(): string[] {
    return this.serverConfig.allowedDomains;
  }

  // Environment-specific configurations
  public getLogConfig(): {
    level: string;
    pretty: boolean;
    auditPath?: string;
  } {
    return {
      level: this.serverConfig.logLevel,
      pretty: this.isDevelopment(),
      auditPath: process.env.AUDIT_LOG_PATH,
    };
  }

  public getSecurityConfig(): {
    encryptionKey: string;
    jwtSecret: string;
    corsOrigins: string[];
    helmetEnabled: boolean;
  } {
    return {
      encryptionKey: this.serverConfig.encryptionKey,
      jwtSecret: this.serverConfig.jwtSecret,
      corsOrigins: this.serverConfig.corsOrigins,
      helmetEnabled: !this.isDevelopment(), // Disable helmet in development
    };
  }

  // Validation helpers
  public validateGoogleScopes(scopes: string[]): boolean {
    const validScopes = [
      'https://mail.google.com/',
      'https://www.googleapis.com/auth/gmail.readonly',
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/drive.readonly',
      'https://www.googleapis.com/auth/documents',
      'https://www.googleapis.com/auth/documents.readonly',
      'https://www.googleapis.com/auth/spreadsheets',
      'https://www.googleapis.com/auth/spreadsheets.readonly',
      'https://www.googleapis.com/auth/presentations',
      'https://www.googleapis.com/auth/presentations.readonly',
      'https://www.googleapis.com/auth/calendar',
      'https://www.googleapis.com/auth/calendar.readonly',
      'https://www.googleapis.com/auth/contacts',
      'https://www.googleapis.com/auth/contacts.readonly',
      'https://www.googleapis.com/auth/tasks',
      'https://www.googleapis.com/auth/tasks.readonly',
      'https://www.googleapis.com/auth/keep',
      'https://www.googleapis.com/auth/keep.readonly',
      'https://www.googleapis.com/auth/forms.body',
      'https://www.googleapis.com/auth/forms.responses.readonly',
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/userinfo.profile',
    ];

    return scopes.every(scope => validScopes.includes(scope));
  }
}
