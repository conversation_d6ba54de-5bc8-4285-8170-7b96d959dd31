import pino from 'pino';
import { v4 as uuidv4 } from 'uuid';

// Create logger instance
const isDevelopment = process.env.NODE_ENV === 'development';
const logLevel = process.env.LOG_LEVEL || 'info';

export const logger = pino({
  level: logLevel,
  transport: isDevelopment
    ? {
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'SYS:standard',
          ignore: 'pid,hostname',
        },
      }
    : undefined,
  formatters: {
    level: (label: string) => {
      return { level: label };
    },
  },
  timestamp: pino.stdTimeFunctions.isoTime,
  base: {
    pid: process.pid,
    hostname: process.env.HOSTNAME || 'unknown',
  },
});

// Request logger middleware
export interface RequestContext {
  requestId: string;
  userId?: string;
  userEmail?: string;
  method: string;
  url: string;
  userAgent?: string;
  ip?: string;
  startTime: number;
}

export class RequestLogger {
  private static contexts = new Map<string, RequestContext>();

  public static startRequest(
    method: string,
    url: string,
    options: {
      userId?: string;
      userEmail?: string;
      userAgent?: string;
      ip?: string;
    } = {}
  ): string {
    const requestId = uuidv4();
    const context: RequestContext = {
      requestId,
      userId: options.userId,
      userEmail: options.userEmail,
      method,
      url,
      userAgent: options.userAgent,
      ip: options.ip,
      startTime: Date.now(),
    };

    this.contexts.set(requestId, context);

    logger.info('Request started', {
      requestId,
      method,
      url,
      userId: options.userId,
      userEmail: options.userEmail,
      ip: options.ip,
    });

    return requestId;
  }

  public static endRequest(
    requestId: string,
    statusCode: number,
    options: {
      error?: Error;
      responseSize?: number;
      additionalData?: Record<string, unknown>;
    } = {}
  ): void {
    const context = this.contexts.get(requestId);
    if (!context) {
      logger.warn('Request context not found', { requestId });
      return;
    }

    const duration = Date.now() - context.startTime;
    const logData = {
      requestId,
      method: context.method,
      url: context.url,
      statusCode,
      duration,
      userId: context.userId,
      userEmail: context.userEmail,
      ip: context.ip,
      responseSize: options.responseSize,
      ...options.additionalData,
    };

    if (options.error) {
      logger.error('Request completed with error', {
        ...logData,
        error: {
          message: options.error.message,
          stack: options.error.stack,
          name: options.error.name,
        },
      });
    } else if (statusCode >= 400) {
      logger.warn('Request completed with client/server error', logData);
    } else {
      logger.info('Request completed successfully', logData);
    }

    this.contexts.delete(requestId);
  }

  public static getContext(requestId: string): RequestContext | undefined {
    return this.contexts.get(requestId);
  }

  public static cleanup(): void {
    const now = Date.now();
    const maxAge = 5 * 60 * 1000; // 5 minutes

    for (const [requestId, context] of this.contexts.entries()) {
      if (now - context.startTime > maxAge) {
        logger.warn('Cleaning up stale request context', { requestId });
        this.contexts.delete(requestId);
      }
    }
  }
}

// Tool execution logger
export class ToolLogger {
  public static logToolStart(
    requestId: string,
    toolName: string,
    userId: string,
    parameters: Record<string, unknown>
  ): void {
    logger.info('Tool execution started', {
      requestId,
      toolName,
      userId,
      parameters: this.sanitizeParameters(parameters),
    });
  }

  public static logToolSuccess(
    requestId: string,
    toolName: string,
    userId: string,
    result: unknown,
    duration: number
  ): void {
    logger.info('Tool execution completed successfully', {
      requestId,
      toolName,
      userId,
      duration,
      resultType: typeof result,
      resultSize: this.getResultSize(result),
    });
  }

  public static logToolError(
    requestId: string,
    toolName: string,
    userId: string,
    error: Error,
    duration: number
  ): void {
    logger.error('Tool execution failed', {
      requestId,
      toolName,
      userId,
      duration,
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name,
      },
    });
  }

  private static sanitizeParameters(parameters: Record<string, unknown>): Record<string, unknown> {
    const sanitized = { ...parameters };
    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'auth', 'credential'];

    for (const [key, value] of Object.entries(sanitized)) {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      } else if (typeof value === 'string' && value.length > 1000) {
        sanitized[key] = `[TRUNCATED: ${value.length} chars]`;
      }
    }

    return sanitized;
  }

  private static getResultSize(result: unknown): number {
    try {
      return JSON.stringify(result).length;
    } catch {
      return 0;
    }
  }
}

// Performance monitoring
export class PerformanceLogger {
  private static timers = new Map<string, number>();

  public static startTimer(name: string): void {
    this.timers.set(name, Date.now());
  }

  public static endTimer(name: string, additionalData?: Record<string, unknown>): number {
    const startTime = this.timers.get(name);
    if (!startTime) {
      logger.warn('Timer not found', { timerName: name });
      return 0;
    }

    const duration = Date.now() - startTime;
    this.timers.delete(name);

    logger.debug('Performance measurement', {
      timerName: name,
      duration,
      ...additionalData,
    });

    return duration;
  }

  public static measureAsync<T>(
    name: string,
    fn: () => Promise<T>,
    additionalData?: Record<string, unknown>
  ): Promise<T> {
    this.startTimer(name);
    return fn()
      .then(result => {
        this.endTimer(name, additionalData);
        return result;
      })
      .catch(error => {
        this.endTimer(name, { ...additionalData, error: true });
        throw error;
      });
  }
}

// Cleanup interval for request contexts
setInterval(() => {
  RequestLogger.cleanup();
}, 60000); // Clean up every minute
