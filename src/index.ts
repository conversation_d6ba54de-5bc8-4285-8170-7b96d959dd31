#!/usr/bin/env node

import { config } from 'dotenv';
import { logger } from '@/utils/logger';
import { ConfigManager } from '@/utils/config';
import { DatabaseManager } from '@/db/database';
import { ToolLoader } from '@/tools';
import { ManifestGenerator } from '@/mcp/manifest';
import { MCPServer } from '@/server';

// Load environment variables
config();

async function main(): Promise<void> {
  try {
    logger.info('Starting Google Workspace MCP Server...');

    // Initialize configuration
    const configManager = ConfigManager.getInstance();
    const serverConfig = configManager.getServerConfig();
    
    logger.info('Configuration loaded', {
      nodeEnv: serverConfig.nodeEnv,
      port: serverConfig.port,
      logLevel: serverConfig.logLevel,
    });

    // Initialize database
    const db = DatabaseManager.getInstance(serverConfig.dbUrl);
    const dbStats = db.getStats();
    
    logger.info('Database initialized', {
      dbUrl: serverConfig.dbUrl,
      stats: dbStats,
    });

    // Load all tools
    const toolLoader = ToolLoader.getInstance();
    toolLoader.loadAllTools();
    
    logger.info('Tools loaded', {
      totalTools: toolLoader.getLoadedToolsCount(),
      gmailTools: toolLoader.getToolsByNamespace('gmail'),
    });

    // Generate manifests
    const manifestGenerator = ManifestGenerator.getInstance();
    manifestGenerator.saveManifests('.');
    
    logger.info('Manifests generated successfully');

    // Start server
    const server = new MCPServer();
    await server.start();

    // Setup graceful shutdown
    const gracefulShutdown = async (signal: string): Promise<void> => {
      logger.info(`Received ${signal}, shutting down gracefully...`);
      
      try {
        await server.stop();
        process.exit(0);
      } catch (error) {
        logger.error('Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Setup periodic cleanup
    const cleanupInterval = setInterval(() => {
      try {
        db.cleanup();
        logger.debug('Periodic cleanup completed');
      } catch (error) {
        logger.error('Periodic cleanup failed:', error);
      }
    }, 60 * 60 * 1000); // Every hour

    // Clear cleanup interval on shutdown
    process.on('exit', () => {
      clearInterval(cleanupInterval);
    });

    logger.info('Google Workspace MCP Server started successfully');

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Start the application
if (require.main === module) {
  main().catch((error) => {
    logger.error('Application startup failed:', error);
    process.exit(1);
  });
}

export { main };
