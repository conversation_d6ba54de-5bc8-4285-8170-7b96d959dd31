import { writeFileSync } from 'fs';
import { join } from 'path';
import { ToolRegistry } from './tool-registry';
import { ConfigManager } from '@/utils/config';
import { logger } from '@/utils/logger';
import type { MCPExample } from '@/types';

export interface MCPManifest {
  name: string;
  version: string;
  description: string;
  author: string;
  license: string;
  homepage?: string;
  repository?: string;
  keywords: string[];
  mcp: {
    version: string;
    capabilities: string[];
    authentication: {
      methods: string[];
      scopes: string[];
    };
    rateLimit: {
      enabled: boolean;
      windowMs: number;
      maxRequests: number;
    };
    security: {
      rbac: boolean;
      encryption: boolean;
      audit: boolean;
    };
  };
  tools: {
    [namespace: string]: {
      version: string;
      description: string;
      tools: {
        [toolName: string]: {
          description: string;
          inputSchema: object;
          outputSchema?: object;
          requiredScopes: string[];
          idempotent: boolean;
          safeMode: boolean;
          deprecated: boolean;
          replacedBy?: string;
          examples?: MCPExample[];
        };
      };
    };
  };
}

export interface ToolsManifest {
  version: string;
  tools: {
    [toolName: string]: {
      name: string;
      description: string;
      inputSchema: object;
      requiredScopes: string[];
      examples?: MCPExample[];
    };
  };
}

export class ManifestGenerator {
  private static instance: ManifestGenerator;
  private toolRegistry = ToolRegistry.getInstance();
  private config = ConfigManager.getInstance();

  private constructor() {}

  public static getInstance(): ManifestGenerator {
    if (!ManifestGenerator.instance) {
      ManifestGenerator.instance = new ManifestGenerator();
    }
    return ManifestGenerator.instance;
  }

  public generateMCPManifest(): MCPManifest {
    const toolManifest = this.toolRegistry.getToolManifest();
    const serverConfig = this.config.getServerConfig();
    const rateLimitConfig = this.config.getRateLimitConfig();
    const featureFlags = this.config.getFeatureFlags();

    // Group tools by namespace
    const toolsByNamespace: MCPManifest['tools'] = {};
    
    for (const tool of toolManifest.tools) {
      if (!toolsByNamespace[tool.namespace]) {
        toolsByNamespace[tool.namespace] = {
          version: tool.version,
          description: this.getNamespaceDescription(tool.namespace),
          tools: {},
        };
      }

      const toolName = tool.name.replace(`${tool.namespace}.`, '');
      toolsByNamespace[tool.namespace].tools[toolName] = {
        description: tool.description,
        inputSchema: this.zodSchemaToJsonSchema(tool.inputSchema),
        requiredScopes: tool.requiredScopes,
        idempotent: tool.idempotent,
        safeMode: tool.safeMode,
        deprecated: tool.deprecated,
        replacedBy: tool.replacedBy,
        examples: tool.examples,
      };
    }

    return {
      name: 'google-workspace-mcp',
      version: '1.0.0',
      description: 'Production-ready MCP server for Google Workspace with full feature coverage',
      author: 'Google Workspace MCP Team',
      license: 'MIT',
      homepage: 'https://github.com/your-org/google-workspace-mcp',
      repository: 'https://github.com/your-org/google-workspace-mcp.git',
      keywords: [
        'mcp',
        'google-workspace',
        'gmail',
        'drive',
        'docs',
        'sheets',
        'slides',
        'calendar',
        'forms',
        'contacts',
        'tasks',
        'keep',
      ],
      mcp: {
        version: '1.0.0',
        capabilities: [
          'tools',
          'resources',
          'prompts',
          'logging',
          'sampling',
        ],
        authentication: {
          methods: ['oauth2', 'service_account'],
          scopes: this.getAllRequiredScopes(),
        },
        rateLimit: {
          enabled: true,
          windowMs: rateLimitConfig.windowMs,
          maxRequests: rateLimitConfig.maxRequests,
        },
        security: {
          rbac: true,
          encryption: true,
          audit: featureFlags.auditLogEnabled,
        },
      },
      tools: toolsByNamespace,
    };
  }

  public generateToolsManifest(): ToolsManifest {
    const toolManifest = this.toolRegistry.getToolManifest();
    const tools: ToolsManifest['tools'] = {};

    for (const tool of toolManifest.tools) {
      tools[tool.name] = {
        name: tool.name,
        description: tool.description,
        inputSchema: this.zodSchemaToJsonSchema(tool.inputSchema),
        requiredScopes: tool.requiredScopes,
        examples: tool.examples,
      };
    }

    return {
      version: '1.0.0',
      tools,
    };
  }

  public generateOpenAPISpec(): object {
    const toolManifest = this.toolRegistry.getToolManifest();
    const serverConfig = this.config.getServerConfig();

    const paths: Record<string, object> = {};
    const components = {
      schemas: {} as Record<string, object>,
      securitySchemes: {
        OAuth2: {
          type: 'oauth2',
          flows: {
            authorizationCode: {
              authorizationUrl: '/auth/google',
              tokenUrl: '/auth/callback',
              scopes: this.getScopeDescriptions(),
            },
          },
        },
        BearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    };

    // Generate paths for each tool
    for (const tool of toolManifest.tools) {
      const pathName = `/tools/${tool.name.replace('.', '/')}`;
      const schemaName = `${tool.name.replace('.', '_')}_Input`;
      
      // Add schema to components
      components.schemas[schemaName] = this.zodSchemaToJsonSchema(tool.inputSchema);

      paths[pathName] = {
        post: {
          summary: tool.description,
          tags: [tool.namespace],
          security: [{ OAuth2: tool.requiredScopes }, { BearerAuth: [] }],
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: {
                  $ref: `#/components/schemas/${schemaName}`,
                },
              },
            },
          },
          responses: {
            200: {
              description: 'Tool executed successfully',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      success: { type: 'boolean' },
                      data: { type: 'object' },
                      metadata: { type: 'object' },
                    },
                  },
                },
              },
            },
            400: {
              description: 'Invalid request parameters',
            },
            401: {
              description: 'Authentication required',
            },
            403: {
              description: 'Insufficient permissions',
            },
            429: {
              description: 'Rate limit exceeded',
            },
            500: {
              description: 'Internal server error',
            },
          },
        },
      };
    }

    return {
      openapi: '3.0.3',
      info: {
        title: 'Google Workspace MCP API',
        version: '1.0.0',
        description: 'Production-ready MCP server for Google Workspace with full feature coverage',
        contact: {
          name: 'Google Workspace MCP Team',
          url: 'https://github.com/your-org/google-workspace-mcp',
        },
        license: {
          name: 'MIT',
          url: 'https://opensource.org/licenses/MIT',
        },
      },
      servers: [
        {
          url: `http://localhost:${serverConfig.port}`,
          description: 'Development server',
        },
      ],
      paths,
      components,
      tags: this.toolRegistry.getNamespaces().map(namespace => ({
        name: namespace,
        description: this.getNamespaceDescription(namespace),
      })),
    };
  }

  public saveManifests(outputDir: string = '.'): void {
    try {
      // Generate manifests
      const mcpManifest = this.generateMCPManifest();
      const toolsManifest = this.generateToolsManifest();
      const openApiSpec = this.generateOpenAPISpec();

      // Save to files
      writeFileSync(
        join(outputDir, 'manifest.json'),
        JSON.stringify(mcpManifest, null, 2)
      );

      writeFileSync(
        join(outputDir, 'tools.json'),
        JSON.stringify(toolsManifest, null, 2)
      );

      writeFileSync(
        join(outputDir, 'openapi.json'),
        JSON.stringify(openApiSpec, null, 2)
      );

      logger.info('Manifests saved successfully', {
        outputDir,
        files: ['manifest.json', 'tools.json', 'openapi.json'],
      });
    } catch (error) {
      logger.error('Failed to save manifests:', error);
      throw error;
    }
  }

  private zodSchemaToJsonSchema(schema: any): object {
    // This is a simplified conversion - in production you'd use a proper library
    // like zod-to-json-schema
    if (schema._def) {
      const def = schema._def;
      
      if (def.typeName === 'ZodObject') {
        const properties: Record<string, object> = {};
        const required: string[] = [];
        
        for (const [key, value] of Object.entries(def.shape())) {
          properties[key] = this.zodSchemaToJsonSchema(value);
          if (!(value as any).isOptional()) {
            required.push(key);
          }
        }
        
        return {
          type: 'object',
          properties,
          required,
        };
      }
      
      if (def.typeName === 'ZodString') {
        return { type: 'string' };
      }
      
      if (def.typeName === 'ZodNumber') {
        return { type: 'number' };
      }
      
      if (def.typeName === 'ZodBoolean') {
        return { type: 'boolean' };
      }
      
      if (def.typeName === 'ZodArray') {
        return {
          type: 'array',
          items: this.zodSchemaToJsonSchema(def.type),
        };
      }
    }
    
    return { type: 'object' };
  }

  private getAllRequiredScopes(): string[] {
    const scopes = new Set<string>();
    const toolManifest = this.toolRegistry.getToolManifest();
    
    for (const tool of toolManifest.tools) {
      for (const scope of tool.requiredScopes) {
        scopes.add(scope);
      }
    }
    
    return Array.from(scopes).sort();
  }

  private getScopeDescriptions(): Record<string, string> {
    return {
      'https://mail.google.com/': 'Full access to Gmail',
      'https://www.googleapis.com/auth/gmail.readonly': 'Read-only access to Gmail',
      'https://www.googleapis.com/auth/drive': 'Full access to Google Drive',
      'https://www.googleapis.com/auth/drive.readonly': 'Read-only access to Google Drive',
      'https://www.googleapis.com/auth/documents': 'Full access to Google Docs',
      'https://www.googleapis.com/auth/documents.readonly': 'Read-only access to Google Docs',
      'https://www.googleapis.com/auth/spreadsheets': 'Full access to Google Sheets',
      'https://www.googleapis.com/auth/spreadsheets.readonly': 'Read-only access to Google Sheets',
      'https://www.googleapis.com/auth/presentations': 'Full access to Google Slides',
      'https://www.googleapis.com/auth/presentations.readonly': 'Read-only access to Google Slides',
      'https://www.googleapis.com/auth/calendar': 'Full access to Google Calendar',
      'https://www.googleapis.com/auth/calendar.readonly': 'Read-only access to Google Calendar',
      'https://www.googleapis.com/auth/contacts': 'Full access to Google Contacts',
      'https://www.googleapis.com/auth/contacts.readonly': 'Read-only access to Google Contacts',
      'https://www.googleapis.com/auth/tasks': 'Full access to Google Tasks',
      'https://www.googleapis.com/auth/tasks.readonly': 'Read-only access to Google Tasks',
      'https://www.googleapis.com/auth/keep': 'Full access to Google Keep',
      'https://www.googleapis.com/auth/keep.readonly': 'Read-only access to Google Keep',
      'https://www.googleapis.com/auth/forms.body': 'Full access to Google Forms',
      'https://www.googleapis.com/auth/forms.responses.readonly': 'Read-only access to Google Forms responses',
    };
  }

  private getNamespaceDescription(namespace: string): string {
    const descriptions: Record<string, string> = {
      gmail: 'Gmail email management tools',
      drive: 'Google Drive file management tools',
      docs: 'Google Docs document management tools',
      sheets: 'Google Sheets spreadsheet management tools',
      slides: 'Google Slides presentation management tools',
      forms: 'Google Forms survey management tools',
      calendar: 'Google Calendar event management tools',
      contacts: 'Google Contacts management tools',
      tasks: 'Google Tasks management tools',
      keep: 'Google Keep notes management tools',
      ops: 'Cross-application workflow automation tools',
    };
    
    return descriptions[namespace] || `${namespace} tools`;
  }
}
