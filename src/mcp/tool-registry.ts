import { z } from 'zod';
import { logger } from '@/utils/logger';
import { RBACService } from '@/auth/rbac';
import { RateLimiterService } from '@/auth/rate-limiter';
import { AuditRepository } from '@/db/repositories/audit';
import { ToolLogger, PerformanceLogger } from '@/utils/logger';
import type {
  MCPTool,
  MCPContext,
  MCPResult,
  MCPExample,
  ValidationError,
  AuthorizationError,
  RateLimitError
} from '@/types';

export interface ToolRegistration {
  tool: MCPTool;
  namespace: string;
  version: string;
  deprecated?: boolean;
  replacedBy?: string;
}

export class ToolRegistry {
  private static instance: ToolRegistry;
  private tools = new Map<string, ToolRegistration>();
  private namespaces = new Set<string>();
  private rbac = RBACService.getInstance();
  private rateLimiter = RateLimiterService.getInstance();
  private auditRepo = new AuditRepository();

  private constructor() {
    logger.info('Tool registry initialized');
  }

  public static getInstance(): ToolRegistry {
    if (!ToolRegistry.instance) {
      ToolRegistry.instance = new ToolRegistry();
    }
    return ToolRegistry.instance;
  }

  public registerTool(
    namespace: string,
    tool: MCPTool,
    version: string = '1.0.0',
    options: {
      deprecated?: boolean;
      replacedBy?: string;
    } = {}
  ): void {
    const fullName = `${namespace}.${tool.name}`;

    if (this.tools.has(fullName)) {
      logger.warn(`Tool ${fullName} is being overridden`);
    }

    this.tools.set(fullName, {
      tool,
      namespace,
      version,
      deprecated: options.deprecated,
      replacedBy: options.replacedBy,
    });

    this.namespaces.add(namespace);

    logger.info(`Registered tool: ${fullName}`, {
      namespace,
      version,
      deprecated: options.deprecated,
      requiredScopes: tool.requiredScopes,
      idempotent: tool.idempotent,
      safeMode: tool.safeMode,
    });
  }

  public getTool(name: string): ToolRegistration | undefined {
    return this.tools.get(name);
  }

  public getAllTools(): Map<string, ToolRegistration> {
    return new Map(this.tools);
  }

  public getToolsByNamespace(namespace: string): Map<string, ToolRegistration> {
    const namespacedTools = new Map<string, ToolRegistration>();

    for (const [name, registration] of this.tools) {
      if (registration.namespace === namespace) {
        namespacedTools.set(name, registration);
      }
    }

    return namespacedTools;
  }

  public getNamespaces(): string[] {
    return Array.from(this.namespaces).sort();
  }

  public async executeTool(
    toolName: string,
    parameters: unknown,
    context: MCPContext
  ): Promise<MCPResult> {
    const startTime = Date.now();
    const timerName = `tool_execution_${toolName}`;
    PerformanceLogger.startTimer(timerName);

    try {
      // Get tool registration
      const registration = this.getTool(toolName);
      if (!registration) {
        throw new ValidationError(`Tool ${toolName} not found`);
      }

      const { tool } = registration;

      // Log tool execution start
      ToolLogger.logToolStart(context.requestId, toolName, context.userId, parameters as Record<string, unknown>);

      // Check if tool is deprecated
      if (registration.deprecated) {
        logger.warn(`Using deprecated tool: ${toolName}`, {
          replacedBy: registration.replacedBy,
          userId: context.userId,
        });
      }

      // Validate authorization
      const authResult = this.rbac.validateContext(context, toolName, tool.requiredScopes);
      if (!authResult.allowed) {
        throw new AuthorizationError(authResult.reason || 'Access denied');
      }

      // Check rate limits
      const rateLimitResult = await this.rateLimiter.checkRateLimit(context, toolName);
      if (!rateLimitResult.allowed) {
        throw new RateLimitError('Rate limit exceeded', {
          remainingPoints: rateLimitResult.remainingPoints,
          msBeforeNext: rateLimitResult.msBeforeNext,
          totalHits: rateLimitResult.totalHits,
        });
      }

      // Validate input parameters
      let validatedParams: unknown;
      try {
        validatedParams = tool.inputSchema.parse(parameters);
      } catch (error) {
        if (error instanceof z.ZodError) {
          throw new ValidationError('Invalid parameters', {
            issues: error.issues,
          });
        }
        throw error;
      }

      // Execute the tool
      const result = await tool.handler(validatedParams, context);
      const duration = PerformanceLogger.endTimer(timerName);

      // Log successful execution
      ToolLogger.logToolSuccess(context.requestId, toolName, context.userId, result, duration);

      // Audit log
      await this.auditRepo.logToolExecution(
        context.userId,
        toolName,
        'execute',
        context.requestId,
        {
          parameters: parameters as Record<string, unknown>,
          result: 'success',
        }
      );

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // Log error
      ToolLogger.logToolError(
        context.requestId,
        toolName,
        context.userId,
        error instanceof Error ? error : new Error(String(error)),
        duration
      );

      // Audit log
      await this.auditRepo.logToolExecution(
        context.userId,
        toolName,
        'execute',
        context.requestId,
        {
          parameters: parameters as Record<string, unknown>,
          result: 'error',
          errorMessage,
        }
      );

      // Return error result
      return {
        success: false,
        error: errorMessage,
        metadata: {
          toolName,
          duration,
          errorType: error instanceof Error ? error.constructor.name : 'UnknownError',
        },
      };
    }
  }

  public validateToolDefinition(tool: MCPTool): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Check required fields
    if (!tool.name || typeof tool.name !== 'string') {
      errors.push('Tool name is required and must be a string');
    }

    if (!tool.description || typeof tool.description !== 'string') {
      errors.push('Tool description is required and must be a string');
    }

    if (!tool.inputSchema) {
      errors.push('Tool inputSchema is required');
    }

    if (!tool.handler || typeof tool.handler !== 'function') {
      errors.push('Tool handler is required and must be a function');
    }

    if (!Array.isArray(tool.requiredScopes)) {
      errors.push('Tool requiredScopes must be an array');
    }

    // Validate tool name format
    if (tool.name && !/^[a-z][a-z0-9_]*$/.test(tool.name)) {
      errors.push('Tool name must start with lowercase letter and contain only lowercase letters, numbers, and underscores');
    }

    // Validate examples if provided
    if (tool.examples) {
      if (!Array.isArray(tool.examples)) {
        errors.push('Tool examples must be an array');
      } else {
        tool.examples.forEach((example, index) => {
          if (!example.name || typeof example.name !== 'string') {
            errors.push(`Example ${index}: name is required and must be a string`);
          }
          if (!example.description || typeof example.description !== 'string') {
            errors.push(`Example ${index}: description is required and must be a string`);
          }
          if (!example.parameters || typeof example.parameters !== 'object') {
            errors.push(`Example ${index}: parameters is required and must be an object`);
          }
        });
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  public getToolManifest(): {
    version: string;
    namespaces: string[];
    tools: Array<{
      name: string;
      namespace: string;
      description: string;
      version: string;
      inputSchema: z.ZodSchema;
      requiredScopes: string[];
      idempotent: boolean;
      safeMode: boolean;
      deprecated: boolean;
      replacedBy?: string;
      examples?: MCPExample[];
    }>;
    totalTools: number;
  } {
    const tools = Array.from(this.tools.entries()).map(([name, registration]) => ({
      name,
      namespace: registration.namespace,
      description: registration.tool.description,
      version: registration.version,
      inputSchema: registration.tool.inputSchema,
      requiredScopes: registration.tool.requiredScopes,
      idempotent: registration.tool.idempotent || false,
      safeMode: registration.tool.safeMode || false,
      deprecated: registration.deprecated || false,
      replacedBy: registration.replacedBy,
      examples: registration.tool.examples,
    }));

    return {
      version: '1.0.0',
      namespaces: this.getNamespaces(),
      tools,
      totalTools: tools.length,
    };
  }

  public getToolsForRole(role: string): Array<{
    name: string;
    namespace: string;
    description: string;
    allowedForRole: boolean;
  }> {
    const mockContext: MCPContext = {
      userId: 'test',
      userEmail: '<EMAIL>',
      role: role as any,
      scopes: [],
      requestId: 'test',
    };

    return Array.from(this.tools.entries()).map(([name, registration]) => {
      const authResult = this.rbac.validateContext(
        mockContext,
        name,
        registration.tool.requiredScopes
      );

      return {
        name,
        namespace: registration.namespace,
        description: registration.tool.description,
        allowedForRole: authResult.allowed,
      };
    });
  }

  public getToolStats(): {
    totalTools: number;
    toolsByNamespace: Record<string, number>;
    deprecatedTools: number;
    safeModTools: number;
    idempotentTools: number;
  } {
    const stats = {
      totalTools: this.tools.size,
      toolsByNamespace: {} as Record<string, number>,
      deprecatedTools: 0,
      safeModTools: 0,
      idempotentTools: 0,
    };

    for (const registration of this.tools.values()) {
      // Count by namespace
      stats.toolsByNamespace[registration.namespace] = 
        (stats.toolsByNamespace[registration.namespace] || 0) + 1;

      // Count deprecated tools
      if (registration.deprecated) {
        stats.deprecatedTools++;
      }

      // Count safe mode tools
      if (registration.tool.safeMode) {
        stats.safeModTools++;
      }

      // Count idempotent tools
      if (registration.tool.idempotent) {
        stats.idempotentTools++;
      }
    }

    return stats;
  }
}
