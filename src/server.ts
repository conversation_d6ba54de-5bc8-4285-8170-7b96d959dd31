import Fastify, { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import helmet from '@fastify/helmet';
import rateLimit from '@fastify/rate-limit';
import { v4 as uuidv4 } from 'uuid';
import { ConfigManager } from '@/utils/config';
import { logger, RequestLogger } from '@/utils/logger';
import { DatabaseManager } from '@/db/database';
import { ToolRegistry } from '@/mcp/tool-registry';
import { ManifestGenerator } from '@/mcp/manifest';
import { GoogleClientFactory } from '@/auth/google-client';
import { EncryptionService } from '@/auth/encryption';
import { RBACService } from '@/auth/rbac';
import { RateLimiterService } from '@/auth/rate-limiter';
import { UserRepository } from '@/db/repositories/user';
import { ResourceRepository } from '@/db/repositories/resource';
import { AuditRepository } from '@/db/repositories/audit';
import type { MCPContext, UserRole } from '@/types';

export class MCPServer {
  private app: FastifyInstance;
  private config = ConfigManager.getInstance();
  private db = DatabaseManager.getInstance();
  private toolRegistry = ToolRegistry.getInstance();
  private manifestGenerator = ManifestGenerator.getInstance();
  private googleClientFactory: GoogleClientFactory;
  private encryption: EncryptionService;
  private rbac: RBACService;
  private rateLimiter: RateLimiterService;
  private userRepo = new UserRepository();
  private resourceRepo = new ResourceRepository();
  private auditRepo = new AuditRepository();

  constructor() {
    this.app = Fastify({
      logger: false, // We use our own logger
      requestIdHeader: 'x-request-id',
      requestIdLogLabel: 'requestId',
      genReqId: () => uuidv4(),
    });

    this.initializeServices();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  private initializeServices(): void {
    const serverConfig = this.config.getServerConfig();
    const googleConfig = this.config.getGoogleConfig();
    const rateLimitConfig = this.config.getRateLimitConfig();

    // Initialize services
    this.googleClientFactory = GoogleClientFactory.getInstance(googleConfig);
    this.encryption = EncryptionService.getInstance(serverConfig.encryptionKey);
    this.rbac = RBACService.getInstance();
    this.rateLimiter = RateLimiterService.getInstance(rateLimitConfig);

    logger.info('Services initialized successfully');
  }

  private setupMiddleware(): void {
    const securityConfig = this.config.getSecurityConfig();

    // Security middleware
    if (securityConfig.helmetEnabled) {
      this.app.register(helmet, {
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", 'data:', 'https:'],
          },
        },
      });
    }

    // Rate limiting
    this.app.register(rateLimit, {
      max: this.config.getRateLimitConfig().maxRequests,
      timeWindow: this.config.getRateLimitConfig().windowMs,
      keyGenerator: (request: FastifyRequest) => {
        const userId = (request as any).userId || 'anonymous';
        return `global:${userId}:${request.ip}`;
      },
    });

    // Request logging middleware
    this.app.addHook('onRequest', async (request: FastifyRequest, reply: FastifyReply) => {
      const requestId = RequestLogger.startRequest(
        request.method,
        request.url,
        {
          userAgent: request.headers['user-agent'],
          ip: request.ip,
        }
      );
      
      (request as any).requestId = requestId;
    });

    this.app.addHook('onResponse', async (request: FastifyRequest, reply: FastifyReply) => {
      const requestId = (request as any).requestId;
      if (requestId) {
        RequestLogger.endRequest(requestId, reply.statusCode, {
          responseSize: reply.getHeader('content-length') as number,
        });
      }
    });

    // Authentication middleware
    this.app.addHook('preHandler', async (request: FastifyRequest, reply: FastifyReply) => {
      // Skip auth for public endpoints
      const publicPaths = ['/health', '/manifest', '/tools', '/openapi', '/auth/google', '/auth/callback'];
      if (publicPaths.some(path => request.url.startsWith(path))) {
        return;
      }

      // Extract and validate authentication
      const authHeader = request.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        reply.code(401).send({ error: 'Authentication required' });
        return;
      }

      // For now, we'll implement a simple token validation
      // In production, you'd validate JWT tokens properly
      const token = authHeader.substring(7);
      const userId = await this.validateToken(token);
      
      if (!userId) {
        reply.code(401).send({ error: 'Invalid token' });
        return;
      }

      const user = await this.userRepo.findById(userId);
      if (!user) {
        reply.code(401).send({ error: 'User not found' });
        return;
      }

      // Add user context to request
      (request as any).userId = user.id;
      (request as any).userEmail = user.email;
      (request as any).userRole = user.role;
      (request as any).userScopes = user.scopes;
    });

    logger.info('Middleware setup completed');
  }

  private setupRoutes(): void {
    // Health check
    this.app.get('/health', async (request: FastifyRequest, reply: FastifyReply) => {
      const dbHealth = this.db.healthCheck();
      const stats = this.db.getStats();
      
      reply.send({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        database: {
          healthy: dbHealth,
          stats,
        },
        tools: this.toolRegistry.getToolStats(),
      });
    });

    // Manifest endpoints
    this.app.get('/manifest', async (request: FastifyRequest, reply: FastifyReply) => {
      const manifest = this.manifestGenerator.generateMCPManifest();
      reply.send(manifest);
    });

    this.app.get('/tools', async (request: FastifyRequest, reply: FastifyReply) => {
      const toolsManifest = this.manifestGenerator.generateToolsManifest();
      reply.send(toolsManifest);
    });

    this.app.get('/openapi', async (request: FastifyRequest, reply: FastifyReply) => {
      const openApiSpec = this.manifestGenerator.generateOpenAPISpec();
      reply.send(openApiSpec);
    });

    // Authentication routes
    this.setupAuthRoutes();

    // Tool execution routes
    this.setupToolRoutes();

    // Admin routes
    this.setupAdminRoutes();

    logger.info('Routes setup completed');
  }

  private setupAuthRoutes(): void {
    // OAuth initiation
    this.app.get('/auth/google', async (request: FastifyRequest, reply: FastifyReply) => {
      const scopes = this.config.getDefaultScopes();
      const state = uuidv4();
      const authUrl = this.googleClientFactory.getAuthUrl(scopes, state);
      
      reply.redirect(authUrl);
    });

    // OAuth callback
    this.app.get('/auth/callback', async (request: FastifyRequest, reply: FastifyReply) => {
      const { code, state, error } = request.query as {
        code?: string;
        state?: string;
        error?: string;
      };

      if (error) {
        reply.code(400).send({ error: `OAuth error: ${error}` });
        return;
      }

      if (!code) {
        reply.code(400).send({ error: 'Authorization code not provided' });
        return;
      }

      try {
        // Exchange code for tokens
        const tokens = await this.googleClientFactory.exchangeCodeForTokens(code);
        
        // Get user info
        const oauthClient = this.googleClientFactory.createOAuth2Client(tokens);
        const userInfo = await this.googleClientFactory.getUserInfo(oauthClient);
        
        // Create or update user
        let user = await this.userRepo.findByEmail(userInfo.email);
        if (!user) {
          user = await this.userRepo.create({
            email: userInfo.email,
            name: userInfo.name,
            role: UserRole.VIEWER, // Default role
            domains: [userInfo.email.split('@')[1]],
            scopes: this.config.getDefaultScopes(),
          });
        }

        // Store tokens
        await this.userRepo.storeTokens(user.id, tokens);
        
        // Update last login
        await this.userRepo.updateLastLogin(user.id);

        // Generate session token (simplified - use proper JWT in production)
        const sessionToken = this.generateSessionToken(user.id);

        reply.send({
          success: true,
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
          },
          token: sessionToken,
        });

      } catch (error) {
        logger.error('OAuth callback error:', error);
        reply.code(500).send({ error: 'Authentication failed' });
      }
    });

    // Token refresh
    this.app.post('/auth/refresh', async (request: FastifyRequest, reply: FastifyReply) => {
      const userId = (request as any).userId;
      
      try {
        const tokens = await this.userRepo.getTokens(userId);
        if (!tokens) {
          reply.code(401).send({ error: 'No tokens found' });
          return;
        }

        const oauthClient = this.googleClientFactory.createOAuth2Client(tokens);
        const { credentials } = await oauthClient.refreshAccessToken();
        
        const updatedTokens = {
          ...tokens,
          access_token: credentials.access_token!,
          expires_at: credentials.expiry_date!,
        };

        await this.userRepo.storeTokens(userId, updatedTokens);
        
        reply.send({ success: true, message: 'Tokens refreshed' });
      } catch (error) {
        logger.error('Token refresh error:', error);
        reply.code(500).send({ error: 'Token refresh failed' });
      }
    });

    // Logout
    this.app.post('/auth/logout', async (request: FastifyRequest, reply: FastifyReply) => {
      const userId = (request as any).userId;
      
      try {
        await this.userRepo.revokeTokens(userId);
        reply.send({ success: true, message: 'Logged out successfully' });
      } catch (error) {
        logger.error('Logout error:', error);
        reply.code(500).send({ error: 'Logout failed' });
      }
    });
  }

  private setupToolRoutes(): void {
    // Execute tool
    this.app.post('/tools/:namespace/:toolName', async (request: FastifyRequest, reply: FastifyReply) => {
      const { namespace, toolName } = request.params as { namespace: string; toolName: string };
      const parameters = request.body;
      const requestId = (request as any).requestId;
      const userId = (request as any).userId;
      const userEmail = (request as any).userEmail;
      const userRole = (request as any).userRole;
      const userScopes = (request as any).userScopes;

      const fullToolName = `${namespace}.${toolName}`;
      
      const context: MCPContext = {
        userId,
        userEmail,
        role: userRole,
        scopes: userScopes,
        requestId,
      };

      try {
        const result = await this.toolRegistry.executeTool(fullToolName, parameters, context);
        reply.send(result);
      } catch (error) {
        logger.error(`Tool execution error for ${fullToolName}:`, error);
        reply.code(500).send({
          success: false,
          error: error instanceof Error ? error.message : 'Tool execution failed',
        });
      }
    });
  }

  private setupAdminRoutes(): void {
    // Admin routes would go here
    // For now, just a placeholder
    this.app.get('/admin/stats', async (request: FastifyRequest, reply: FastifyReply) => {
      const userRole = (request as any).userRole;
      
      if (userRole !== UserRole.ADMIN) {
        reply.code(403).send({ error: 'Admin access required' });
        return;
      }

      const stats = {
        database: this.db.getStats(),
        tools: this.toolRegistry.getToolStats(),
        // Add more admin stats as needed
      };

      reply.send(stats);
    });
  }

  private setupErrorHandling(): void {
    this.app.setErrorHandler(async (error, request, reply) => {
      const requestId = (request as any).requestId;
      
      logger.error('Unhandled error:', {
        requestId,
        error: {
          message: error.message,
          stack: error.stack,
          name: error.name,
        },
        request: {
          method: request.method,
          url: request.url,
          headers: request.headers,
        },
      });

      if (requestId) {
        RequestLogger.endRequest(requestId, 500, { error });
      }

      reply.code(500).send({
        error: 'Internal server error',
        requestId,
      });
    });
  }

  private async validateToken(token: string): Promise<string | null> {
    // Simplified token validation - implement proper JWT validation in production
    try {
      // For now, just check if it's a valid UUID format
      if (token.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        return token;
      }
      return null;
    } catch {
      return null;
    }
  }

  private generateSessionToken(userId: string): string {
    // Simplified session token generation - use proper JWT in production
    return userId;
  }

  public async start(): Promise<void> {
    const serverConfig = this.config.getServerConfig();
    
    try {
      await this.app.listen({
        port: serverConfig.port,
        host: '0.0.0.0',
      });
      
      logger.info(`Server started successfully on port ${serverConfig.port}`, {
        environment: serverConfig.nodeEnv,
        port: serverConfig.port,
      });
    } catch (error) {
      logger.error('Failed to start server:', error);
      throw error;
    }
  }

  public async stop(): Promise<void> {
    try {
      await this.app.close();
      this.db.close();
      logger.info('Server stopped successfully');
    } catch (error) {
      logger.error('Error stopping server:', error);
      throw error;
    }
  }

  public getApp(): FastifyInstance {
    return this.app;
  }
}
