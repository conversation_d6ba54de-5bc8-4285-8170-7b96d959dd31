{"admin": {"allowedTools": ["*"], "allowedScopes": ["https://mail.google.com/", "https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/documents", "https://www.googleapis.com/auth/spreadsheets", "https://www.googleapis.com/auth/presentations", "https://www.googleapis.com/auth/calendar", "https://www.googleapis.com/auth/contacts", "https://www.googleapis.com/auth/tasks", "https://www.googleapis.com/auth/keep", "https://www.googleapis.com/auth/forms.body", "https://www.googleapis.com/auth/forms.responses.readonly"], "rateLimits": {"requestsPerMinute": 1000, "requestsPerHour": 10000, "requestsPerDay": 100000}, "restrictions": {"readOnly": false, "maxFileSize": 104857600, "allowedDomains": [], "allowedResourceTypes": []}}, "editor": {"allowedTools": ["gmail.*", "drive.*", "docs.*", "sheets.*", "slides.*", "forms.*", "calendar.*", "contacts.*", "tasks.*", "keep.*"], "allowedScopes": ["https://mail.google.com/", "https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/documents", "https://www.googleapis.com/auth/spreadsheets", "https://www.googleapis.com/auth/presentations", "https://www.googleapis.com/auth/calendar", "https://www.googleapis.com/auth/contacts", "https://www.googleapis.com/auth/tasks", "https://www.googleapis.com/auth/keep", "https://www.googleapis.com/auth/forms.body", "https://www.googleapis.com/auth/forms.responses.readonly"], "rateLimits": {"requestsPerMinute": 500, "requestsPerHour": 5000, "requestsPerDay": 50000}, "restrictions": {"readOnly": false, "maxFileSize": 52428800, "allowedDomains": [], "allowedResourceTypes": []}}, "viewer": {"allowedTools": ["gmail.search", "gmail.get_message", "gmail.list_threads", "drive.search", "drive.download", "docs.read", "sheets.read_range", "slides.read", "forms.list_responses", "forms.get_response", "calendar.list_events", "contacts.search", "contacts.list", "tasks.list", "keep.search"], "allowedScopes": ["https://www.googleapis.com/auth/gmail.readonly", "https://www.googleapis.com/auth/drive.readonly", "https://www.googleapis.com/auth/documents.readonly", "https://www.googleapis.com/auth/spreadsheets.readonly", "https://www.googleapis.com/auth/presentations.readonly", "https://www.googleapis.com/auth/calendar.readonly", "https://www.googleapis.com/auth/contacts.readonly", "https://www.googleapis.com/auth/tasks.readonly", "https://www.googleapis.com/auth/keep.readonly", "https://www.googleapis.com/auth/forms.responses.readonly"], "rateLimits": {"requestsPerMinute": 100, "requestsPerHour": 1000, "requestsPerDay": 10000}, "restrictions": {"readOnly": true, "maxFileSize": 10485760, "allowedDomains": [], "allowedResourceTypes": ["gmail_message", "gmail_thread", "drive_file", "docs_document", "sheets_spreadsheet", "slides_presentation", "forms_form", "calendar_event", "contacts_person", "tasks_task", "keep_note"]}}}