{"version": "1.0.0", "tools": {"gmail.send_email": {"name": "gmail.send_email", "description": "Send an email through Gmail with optional attachments and threading support", "inputSchema": {"type": "object"}, "requiredScopes": ["https://mail.google.com/"], "examples": [{"name": "Send simple email", "description": "Send a basic email with text content", "parameters": {"to": ["<EMAIL>"], "subject": "Hello from MCP", "text": "This is a test email sent through the MCP Gmail integration."}}, {"name": "Send HTML email with CC", "description": "Send an HTML email with CC recipients", "parameters": {"to": ["<EMAIL>"], "cc": ["<EMAIL>"], "subject": "Project Update", "html": "<h1>Project Status</h1><p>The project is <strong>on track</strong>.</p>"}}]}, "gmail.search": {"name": "gmail.search", "description": "Search Gmail messages using Gmail search syntax", "inputSchema": {"type": "object", "properties": {"query": {"type": "string"}, "maxResults": {"type": "object"}, "pageToken": {"type": "object"}}, "required": ["query"]}, "requiredScopes": ["https://www.googleapis.com/auth/gmail.readonly"], "examples": [{"name": "Search by sender", "description": "Find emails from a specific sender", "parameters": {"query": "from:<EMAIL>", "maxResults": 20}}, {"name": "Search unread emails", "description": "Find all unread emails", "parameters": {"query": "is:unread", "maxResults": 50}}]}, "gmail.get_message": {"name": "gmail.get_message", "description": "Retrieve a specific Gmail message by ID with configurable detail level", "inputSchema": {"type": "object", "properties": {"id": {"type": "string"}, "format": {"type": "object"}}, "required": ["id"]}, "requiredScopes": ["https://www.googleapis.com/auth/gmail.readonly"], "examples": [{"name": "Get full message", "description": "Retrieve complete message details", "parameters": {"id": "18b2e1a2f3c4d5e6", "format": "full"}}, {"name": "Get message metadata only", "description": "Retrieve just the message headers and metadata", "parameters": {"id": "18b2e1a2f3c4d5e6", "format": "metadata"}}]}, "gmail.modify_labels": {"name": "gmail.modify_labels", "description": "Add or remove labels from a Gmail message", "inputSchema": {"type": "object"}, "requiredScopes": ["https://mail.google.com/"], "examples": [{"name": "Mark as important", "description": "Add the important label to a message", "parameters": {"messageId": "18b2e1a2f3c4d5e6", "addLabelIds": ["IMPORTANT"]}}, {"name": "Archive message", "description": "Remove inbox label to archive a message", "parameters": {"messageId": "18b2e1a2f3c4d5e6", "removeLabelIds": ["INBOX"]}}]}, "gmail.list_threads": {"name": "gmail.list_threads", "description": "List Gmail conversation threads with optional search filtering", "inputSchema": {"type": "object", "properties": {"query": {"type": "object"}, "maxResults": {"type": "object"}, "pageToken": {"type": "object"}}, "required": []}, "requiredScopes": ["https://www.googleapis.com/auth/gmail.readonly"], "examples": [{"name": "List recent threads", "description": "Get the most recent conversation threads", "parameters": {"maxResults": 25}}, {"name": "List threads from specific sender", "description": "Get conversation threads from a specific sender", "parameters": {"query": "from:<EMAIL>", "maxResults": 15}}]}, "gmail.watch_start": {"name": "gmail.watch_start", "description": "Start watching for Gmail message changes via push notifications", "inputSchema": {"type": "object", "properties": {"topicName": {"type": "string"}, "labelIds": {"type": "object"}, "labelFilterAction": {"type": "object"}}, "required": ["topicName"]}, "requiredScopes": ["https://mail.google.com/"], "examples": [{"name": "Watch all messages", "description": "Start watching for changes to all messages", "parameters": {"topicName": "projects/my-project/topics/gmail-notifications"}}, {"name": "Watch inbox only", "description": "Watch for changes only to inbox messages", "parameters": {"topicName": "projects/my-project/topics/gmail-notifications", "labelIds": ["INBOX"], "labelFilterAction": "include"}}]}, "gmail.watch_stop": {"name": "gmail.watch_stop", "description": "Stop watching for Gmail message changes", "inputSchema": {"type": "object", "properties": {}, "required": []}, "requiredScopes": ["https://mail.google.com/"], "examples": [{"name": "Stop watching", "description": "Stop all Gmail push notifications", "parameters": {}}]}}}