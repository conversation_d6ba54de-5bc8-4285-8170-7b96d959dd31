import { defineConfig } from 'tsup';

export default defineConfig({
  entry: ['src/index.ts'],
  format: ['cjs', 'esm'],
  dts: true,
  splitting: false,
  sourcemap: true,
  clean: true,
  minify: false,
  target: 'node18',
  outDir: 'dist',
  external: [
    'better-sqlite3',
    'googleapis',
    'google-auth-library',
    '@google-cloud/kms',
    'fastify',
    '@fastify/helmet',
    '@fastify/rate-limit',
    'pino',
    'pino-pretty',
    'rate-limiter-flexible',
    'crypto-js',
    'uuid',
    'mime-types',
    'dompurify',
    'jsdom',
    'zod',
    'dotenv'
  ],
  esbuildOptions(options) {
    options.banner = {
      js: '"use strict";',
    };
  },
});
