{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "NodeNext", "moduleResolution": "NodeNext", "rootDir": "./src", "outDir": "./dist", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "skipLibCheck": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "importHelpers": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/types/*": ["types/*"], "@/utils/*": ["utils/*"], "@/services/*": ["services/*"], "@/tools/*": ["tools/*"], "@/auth/*": ["auth/*"], "@/db/*": ["db/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": true}}