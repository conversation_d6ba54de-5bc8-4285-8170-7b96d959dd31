{"openapi": "3.0.3", "info": {"title": "Google Workspace MCP API", "version": "1.0.0", "description": "Production-ready MCP server for Google Workspace with full feature coverage", "contact": {"name": "Google Workspace MCP Team", "url": "https://github.com/your-org/google-workspace-mcp"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}], "paths": {"/tools/gmail/send_email": {"post": {"summary": "Send an email through Gmail with optional attachments and threading support", "tags": ["gmail"], "security": [{"OAuth2": ["https://mail.google.com/"]}, {"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/gmail_send_email_Input"}}}}, "responses": {"200": {"description": "<PERSON><PERSON> executed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object"}, "metadata": {"type": "object"}}}}}}, "400": {"description": "Invalid request parameters"}, "401": {"description": "Authentication required"}, "403": {"description": "Insufficient permissions"}, "429": {"description": "Rate limit exceeded"}, "500": {"description": "Internal server error"}}}}, "/tools/gmail/search": {"post": {"summary": "Search Gmail messages using Gmail search syntax", "tags": ["gmail"], "security": [{"OAuth2": ["https://www.googleapis.com/auth/gmail.readonly"]}, {"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/gmail_search_Input"}}}}, "responses": {"200": {"description": "<PERSON><PERSON> executed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object"}, "metadata": {"type": "object"}}}}}}, "400": {"description": "Invalid request parameters"}, "401": {"description": "Authentication required"}, "403": {"description": "Insufficient permissions"}, "429": {"description": "Rate limit exceeded"}, "500": {"description": "Internal server error"}}}}, "/tools/gmail/get_message": {"post": {"summary": "Retrieve a specific Gmail message by ID with configurable detail level", "tags": ["gmail"], "security": [{"OAuth2": ["https://www.googleapis.com/auth/gmail.readonly"]}, {"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/gmail_get_message_Input"}}}}, "responses": {"200": {"description": "<PERSON><PERSON> executed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object"}, "metadata": {"type": "object"}}}}}}, "400": {"description": "Invalid request parameters"}, "401": {"description": "Authentication required"}, "403": {"description": "Insufficient permissions"}, "429": {"description": "Rate limit exceeded"}, "500": {"description": "Internal server error"}}}}, "/tools/gmail/modify_labels": {"post": {"summary": "Add or remove labels from a Gmail message", "tags": ["gmail"], "security": [{"OAuth2": ["https://mail.google.com/"]}, {"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/gmail_modify_labels_Input"}}}}, "responses": {"200": {"description": "<PERSON><PERSON> executed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object"}, "metadata": {"type": "object"}}}}}}, "400": {"description": "Invalid request parameters"}, "401": {"description": "Authentication required"}, "403": {"description": "Insufficient permissions"}, "429": {"description": "Rate limit exceeded"}, "500": {"description": "Internal server error"}}}}, "/tools/gmail/list_threads": {"post": {"summary": "List Gmail conversation threads with optional search filtering", "tags": ["gmail"], "security": [{"OAuth2": ["https://www.googleapis.com/auth/gmail.readonly"]}, {"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/gmail_list_threads_Input"}}}}, "responses": {"200": {"description": "<PERSON><PERSON> executed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object"}, "metadata": {"type": "object"}}}}}}, "400": {"description": "Invalid request parameters"}, "401": {"description": "Authentication required"}, "403": {"description": "Insufficient permissions"}, "429": {"description": "Rate limit exceeded"}, "500": {"description": "Internal server error"}}}}, "/tools/gmail/watch_start": {"post": {"summary": "Start watching for Gmail message changes via push notifications", "tags": ["gmail"], "security": [{"OAuth2": ["https://mail.google.com/"]}, {"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/gmail_watch_start_Input"}}}}, "responses": {"200": {"description": "<PERSON><PERSON> executed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object"}, "metadata": {"type": "object"}}}}}}, "400": {"description": "Invalid request parameters"}, "401": {"description": "Authentication required"}, "403": {"description": "Insufficient permissions"}, "429": {"description": "Rate limit exceeded"}, "500": {"description": "Internal server error"}}}}, "/tools/gmail/watch_stop": {"post": {"summary": "Stop watching for Gmail message changes", "tags": ["gmail"], "security": [{"OAuth2": ["https://mail.google.com/"]}, {"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/gmail_watch_stop_Input"}}}}, "responses": {"200": {"description": "<PERSON><PERSON> executed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object"}, "metadata": {"type": "object"}}}}}}, "400": {"description": "Invalid request parameters"}, "401": {"description": "Authentication required"}, "403": {"description": "Insufficient permissions"}, "429": {"description": "Rate limit exceeded"}, "500": {"description": "Internal server error"}}}}}, "components": {"schemas": {"gmail_send_email_Input": {"type": "object"}, "gmail_search_Input": {"type": "object", "properties": {"query": {"type": "string"}, "maxResults": {"type": "object"}, "pageToken": {"type": "object"}}, "required": ["query"]}, "gmail_get_message_Input": {"type": "object", "properties": {"id": {"type": "string"}, "format": {"type": "object"}}, "required": ["id"]}, "gmail_modify_labels_Input": {"type": "object"}, "gmail_list_threads_Input": {"type": "object", "properties": {"query": {"type": "object"}, "maxResults": {"type": "object"}, "pageToken": {"type": "object"}}, "required": []}, "gmail_watch_start_Input": {"type": "object", "properties": {"topicName": {"type": "string"}, "labelIds": {"type": "object"}, "labelFilterAction": {"type": "object"}}, "required": ["topicName"]}, "gmail_watch_stop_Input": {"type": "object", "properties": {}, "required": []}}, "securitySchemes": {"OAuth2": {"type": "oauth2", "flows": {"authorizationCode": {"authorizationUrl": "/auth/google", "tokenUrl": "/auth/callback", "scopes": {"https://mail.google.com/": "Full access to Gmail", "https://www.googleapis.com/auth/gmail.readonly": "Read-only access to Gmail", "https://www.googleapis.com/auth/drive": "Full access to Google Drive", "https://www.googleapis.com/auth/drive.readonly": "Read-only access to Google Drive", "https://www.googleapis.com/auth/documents": "Full access to Google Docs", "https://www.googleapis.com/auth/documents.readonly": "Read-only access to Google Docs", "https://www.googleapis.com/auth/spreadsheets": "Full access to Google Sheets", "https://www.googleapis.com/auth/spreadsheets.readonly": "Read-only access to Google Sheets", "https://www.googleapis.com/auth/presentations": "Full access to Google Slides", "https://www.googleapis.com/auth/presentations.readonly": "Read-only access to Google Slides", "https://www.googleapis.com/auth/calendar": "Full access to Google Calendar", "https://www.googleapis.com/auth/calendar.readonly": "Read-only access to Google Calendar", "https://www.googleapis.com/auth/contacts": "Full access to Google Contacts", "https://www.googleapis.com/auth/contacts.readonly": "Read-only access to Google Contacts", "https://www.googleapis.com/auth/tasks": "Full access to Google Tasks", "https://www.googleapis.com/auth/tasks.readonly": "Read-only access to Google Tasks", "https://www.googleapis.com/auth/keep": "Full access to Google Keep", "https://www.googleapis.com/auth/keep.readonly": "Read-only access to Google Keep", "https://www.googleapis.com/auth/forms.body": "Full access to Google Forms", "https://www.googleapis.com/auth/forms.responses.readonly": "Read-only access to Google Forms responses"}}}}, "BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "tags": [{"name": "gmail", "description": "Gmail email management tools"}]}