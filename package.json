{"name": "google-workspace-mcp", "version": "1.0.0", "description": "Production-ready MCP server for Google Workspace with full feature coverage", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsup", "start": "node dist/index.js", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "docker:build": "docker build -t google-workspace-mcp .", "docker:run": "docker-compose up", "prepare": "husky install"}, "keywords": ["mcp", "google-workspace", "gmail", "drive", "docs", "sheets", "slides", "calendar", "forms", "nodejs", "typescript"], "author": "Google Workspace MCP Team", "license": "MIT", "dependencies": {"@fastify/helmet": "^11.1.1", "@fastify/rate-limit": "^9.1.0", "@google-cloud/kms": "^4.2.1", "better-sqlite3": "^9.2.2", "dotenv": "^16.3.1", "fastify": "^4.25.2", "googleapis": "^131.0.0", "pino": "^8.17.2", "pino-pretty": "^10.3.1", "rate-limiter-flexible": "^4.0.1", "zod": "^3.22.4", "crypto-js": "^4.2.0", "uuid": "^9.0.1", "mime-types": "^2.1.35", "dompurify": "^3.0.7", "jsdom": "^23.2.0"}, "devDependencies": {"@types/better-sqlite3": "^7.6.8", "@types/crypto-js": "^4.2.2", "@types/dompurify": "^3.0.5", "@types/jsdom": "^21.1.6", "@types/mime-types": "^2.1.4", "@types/node": "^20.11.5", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "@vitest/coverage-v8": "^1.2.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.2.4", "rimraf": "^5.0.5", "supertest": "^6.3.4", "tsup": "^8.0.1", "tsx": "^4.7.0", "typescript": "^5.3.3", "vitest": "^1.2.1"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.1", "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"]}}