#!/bin/bash

# Google Workspace MCP Server - cURL Examples
# 
# This script demonstrates how to interact with the MCP server using cURL
# Make sure to set your AUTH_TOKEN environment variable first

set -e

# Configuration
MCP_SERVER_URL=${MCP_SERVER_URL:-"http://localhost:3000"}
AUTH_TOKEN=${AUTH_TOKEN:-""}

if [ -z "$AUTH_TOKEN" ]; then
    echo "❌ AUTH_TOKEN environment variable is required"
    echo "Please authenticate first by visiting: $MCP_SERVER_URL/auth/google"
    exit 1
fi

echo "🚀 Google Workspace MCP Server - cURL Examples"
echo "Server: $MCP_SERVER_URL"
echo ""

# Helper function to make authenticated requests
make_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    
    echo "📡 $method $endpoint"
    
    if [ -n "$data" ]; then
        curl -s -X "$method" \
             -H "Authorization: Bearer $AUTH_TOKEN" \
             -H "Content-Type: application/json" \
             -d "$data" \
             "$MCP_SERVER_URL$endpoint" | jq '.'
    else
        curl -s -X "$method" \
             -H "Authorization: Bearer $AUTH_TOKEN" \
             "$MCP_SERVER_URL$endpoint" | jq '.'
    fi
    
    echo ""
}

# 1. Health Check
echo "1. 🏥 Health Check"
curl -s "$MCP_SERVER_URL/health" | jq '.'
echo ""

# 2. Get Manifest
echo "2. 📋 Server Manifest"
curl -s "$MCP_SERVER_URL/manifest" | jq '.name, .version, .mcp.capabilities'
echo ""

# 3. Get Tools List
echo "3. 🔧 Available Tools"
curl -s "$MCP_SERVER_URL/tools" | jq '.tools | keys | length'
echo ""

# 4. Gmail - Search Messages
echo "4. 📧 Gmail - Search Messages"
make_request "POST" "/tools/gmail/search" '{
  "query": "is:inbox",
  "maxResults": 5
}'

# 5. Gmail - List Threads
echo "5. 💬 Gmail - List Threads"
make_request "POST" "/tools/gmail/list_threads" '{
  "maxResults": 3
}'

# 6. Gmail - Search Unread Messages
echo "6. 📬 Gmail - Search Unread Messages"
make_request "POST" "/tools/gmail/search" '{
  "query": "is:unread",
  "maxResults": 10
}'

# 7. Gmail - Search by Sender
echo "7. 👤 Gmail - Search by Sender"
make_request "POST" "/tools/gmail/search" '{
  "query": "from:noreply",
  "maxResults": 5
}'

# 8. Gmail - Search by Date Range
echo "8. 📅 Gmail - Search by Date Range"
make_request "POST" "/tools/gmail/search" '{
  "query": "after:2024/01/01 before:2024/12/31",
  "maxResults": 5
}'

# 9. Gmail - Send Test Email (commented out to avoid spam)
echo "9. 📤 Gmail - Send Email (Example - Commented Out)"
echo "# Uncomment the following to send a test email:"
echo "# make_request \"POST\" \"/tools/gmail/send_email\" '{"
echo "#   \"to\": [\"<EMAIL>\"],"
echo "#   \"subject\": \"Test from MCP Server\","
echo "#   \"text\": \"This is a test email sent via cURL through the MCP server.\""
echo "# }'"
echo ""

# 10. Get specific message (requires a message ID from search results)
echo "10. 📄 Gmail - Get Message (Example)"
echo "# To get a specific message, use a message ID from search results:"
echo "# make_request \"POST\" \"/tools/gmail/get_message\" '{"
echo "#   \"id\": \"MESSAGE_ID_HERE\","
echo "#   \"format\": \"metadata\""
echo "# }'"
echo ""

# 11. Modify message labels (requires a message ID)
echo "11. 🏷️  Gmail - Modify Labels (Example)"
echo "# To modify message labels, use a message ID from search results:"
echo "# make_request \"POST\" \"/tools/gmail/modify_labels\" '{"
echo "#   \"messageId\": \"MESSAGE_ID_HERE\","
echo "#   \"addLabelIds\": [\"IMPORTANT\"]"
echo "# }'"
echo ""

# 12. Admin Stats (requires admin role)
echo "12. 📊 Admin Stats (Requires Admin Role)"
echo "# make_request \"GET\" \"/admin/stats\""
echo ""

echo "✅ cURL examples completed!"
echo ""
echo "💡 Tips:"
echo "   - Replace MESSAGE_ID_HERE with actual message IDs from search results"
echo "   - Uncomment the send email example to test email sending"
echo "   - Use different Gmail search operators: is:unread, from:<EMAIL>, subject:keyword"
echo "   - Check the server logs for detailed request/response information"
