#!/usr/bin/env tsx

/**
 * Basic usage example for Google Workspace MCP Server
 * 
 * This script demonstrates how to:
 * 1. Authenticate with the MCP server
 * 2. Execute various Gmail tools
 * 3. Handle responses and errors
 */

import { config } from 'dotenv';

// Load environment variables
config();

const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://localhost:3000';
const AUTH_TOKEN = process.env.AUTH_TOKEN; // Get this from OAuth flow

interface MCPResponse {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: any;
}

class MCPClient {
  private baseUrl: string;
  private token: string;

  constructor(baseUrl: string, token: string) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  private async makeRequest(endpoint: string, data?: any): Promise<MCPResponse> {
    const url = `${this.baseUrl}${endpoint}`;
    
    try {
      const response = await fetch(url, {
        method: data ? 'POST' : 'GET',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json',
        },
        body: data ? JSON.stringify(data) : undefined,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`Request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Gmail methods
  async sendEmail(params: {
    to: string[];
    subject: string;
    text?: string;
    html?: string;
    cc?: string[];
    bcc?: string[];
  }): Promise<MCPResponse> {
    return this.makeRequest('/tools/gmail/send_email', params);
  }

  async searchEmails(params: {
    query: string;
    maxResults?: number;
    pageToken?: string;
  }): Promise<MCPResponse> {
    return this.makeRequest('/tools/gmail/search', params);
  }

  async getMessage(params: {
    id: string;
    format?: 'minimal' | 'full' | 'raw' | 'metadata';
  }): Promise<MCPResponse> {
    return this.makeRequest('/tools/gmail/get_message', params);
  }

  async modifyLabels(params: {
    messageId: string;
    addLabelIds?: string[];
    removeLabelIds?: string[];
  }): Promise<MCPResponse> {
    return this.makeRequest('/tools/gmail/modify_labels', params);
  }

  async listThreads(params: {
    query?: string;
    maxResults?: number;
    pageToken?: string;
  }): Promise<MCPResponse> {
    return this.makeRequest('/tools/gmail/list_threads', params);
  }

  // Utility methods
  async getHealth(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/health`);
    return response.json();
  }

  async getManifest(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/manifest`);
    return response.json();
  }
}

async function main() {
  if (!AUTH_TOKEN) {
    console.error('AUTH_TOKEN environment variable is required');
    console.log('Please authenticate first by visiting: http://localhost:3000/auth/google');
    process.exit(1);
  }

  const client = new MCPClient(MCP_SERVER_URL, AUTH_TOKEN);

  try {
    console.log('🚀 Google Workspace MCP Client Example\n');

    // Check server health
    console.log('1. Checking server health...');
    const health = await client.getHealth();
    console.log('✅ Server is healthy:', health.status);
    console.log(`   Database: ${health.database.healthy ? '✅' : '❌'}`);
    console.log(`   Total tools: ${health.tools.totalTools}\n`);

    // Get server manifest
    console.log('2. Getting server manifest...');
    const manifest = await client.getManifest();
    console.log(`✅ Server: ${manifest.name} v${manifest.version}`);
    console.log(`   Namespaces: ${manifest.tools ? Object.keys(manifest.tools).join(', ') : 'None'}\n`);

    // Search for recent emails
    console.log('3. Searching for recent emails...');
    const searchResult = await client.searchEmails({
      query: 'is:inbox',
      maxResults: 5,
    });

    if (searchResult.success && searchResult.data) {
      console.log(`✅ Found ${searchResult.data.messages?.length || 0} messages`);
      
      if (searchResult.data.messages && searchResult.data.messages.length > 0) {
        // Get details of the first message
        console.log('\n4. Getting details of the first message...');
        const firstMessage = searchResult.data.messages[0];
        const messageResult = await client.getMessage({
          id: firstMessage.id,
          format: 'metadata',
        });

        if (messageResult.success && messageResult.data) {
          const headers = messageResult.data.payload?.headers || [];
          const subject = headers.find((h: any) => h.name === 'Subject')?.value || 'No subject';
          const from = headers.find((h: any) => h.name === 'From')?.value || 'Unknown sender';
          
          console.log(`✅ Message details:`);
          console.log(`   Subject: ${subject}`);
          console.log(`   From: ${from}`);
          console.log(`   Snippet: ${messageResult.data.snippet || 'No snippet'}`);
        }
      }
    } else {
      console.log('❌ Search failed:', searchResult.error);
    }

    // List conversation threads
    console.log('\n5. Listing conversation threads...');
    const threadsResult = await client.listThreads({
      maxResults: 3,
    });

    if (threadsResult.success && threadsResult.data) {
      console.log(`✅ Found ${threadsResult.data.threads?.length || 0} threads`);
      
      threadsResult.data.threads?.forEach((thread: any, index: number) => {
        console.log(`   Thread ${index + 1}: ${thread.snippet.substring(0, 50)}...`);
      });
    } else {
      console.log('❌ Thread listing failed:', threadsResult.error);
    }

    // Send a test email (commented out to avoid spam)
    /*
    console.log('\n6. Sending test email...');
    const sendResult = await client.sendEmail({
      to: ['<EMAIL>'],
      subject: 'Test from Google Workspace MCP',
      text: 'This is a test email sent through the MCP server.',
    });

    if (sendResult.success) {
      console.log('✅ Email sent successfully');
      console.log(`   Message ID: ${sendResult.data?.messageId}`);
      console.log(`   Thread ID: ${sendResult.data?.threadId}`);
    } else {
      console.log('❌ Email sending failed:', sendResult.error);
    }
    */

    console.log('\n🎉 Example completed successfully!');

  } catch (error) {
    console.error('❌ Example failed:', error);
    process.exit(1);
  }
}

// Run the example
if (require.main === module) {
  main().catch(console.error);
}

export { MCPClient };
