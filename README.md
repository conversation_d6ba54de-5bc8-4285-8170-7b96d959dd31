# Google Workspace MCP Server

A production-ready Node.js MCP (Model Context Protocol) server that provides full, secure control over Google Workspace applications including Gmail, Drive, Docs, Sheets, Slides, Forms, Calendar, Contacts, Tasks, and Keep.

## Features

- **Full Google Workspace Coverage**: Complete API coverage for all major Google Workspace applications
- **Dual Authentication**: Support for both OAuth2 (user consent) and Service Account with Domain-Wide Delegation
- **Enterprise Security**: RBAC, rate limiting, encryption, audit logging, and input validation
- **Context Memory**: SQLite-based resource graph with conversational references and cross-app linking
- **Production Ready**: TypeScript, comprehensive testing, Docker support, monitoring, and logging
- **MCP Compliant**: Full MCP protocol implementation with manifest generation and tool registry

## Quick Start

### Prerequisites

- Node.js 18+ and pnpm
- Google Cloud Project with Workspace APIs enabled
- Google Workspace domain (for Service Account delegation)

### Installation

```bash
# Clone the repository
git clone https://github.com/your-org/google-workspace-mcp.git
cd google-workspace-mcp

# Install dependencies
pnpm install

# Copy environment template
cp .env.sample .env

# Edit .env with your configuration
nano .env
```

### Google Cloud Setup

1. **Create a Google Cloud Project**
   ```bash
   gcloud projects create your-project-id
   gcloud config set project your-project-id
   ```

2. **Enable Required APIs**
   ```bash
   gcloud services enable gmail.googleapis.com
   gcloud services enable drive.googleapis.com
   gcloud services enable docs.googleapis.com
   gcloud services enable sheets.googleapis.com
   gcloud services enable slides.googleapis.com
   gcloud services enable forms.googleapis.com
   gcloud services enable calendar-json.googleapis.com
   gcloud services enable people.googleapis.com
   gcloud services enable tasks.googleapis.com
   gcloud services enable keep.googleapis.com
   ```

3. **Create OAuth2 Credentials**
   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Navigate to APIs & Services > Credentials
   - Create OAuth 2.0 Client ID (Web application)
   - Add `http://localhost:3000/auth/callback` to authorized redirect URIs
   - Download the credentials and update your `.env` file

4. **Create Service Account (Optional, for Domain-Wide Delegation)**
   ```bash
   gcloud iam service-accounts create workspace-mcp \
     --display-name="Google Workspace MCP Server"
   
   gcloud iam service-accounts keys create service-account.json \
     --iam-account=<EMAIL>
   ```

5. **Configure Domain-Wide Delegation**
   - Go to Google Admin Console > Security > API Controls
   - Add the service account client ID with required scopes
   - Update `IMPERSONATE_USER_EMAIL` in your `.env` file

### Development

```bash
# Start development server
pnpm dev

# Run tests
pnpm test

# Build for production
pnpm build

# Start production server
pnpm start
```

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f google-workspace-mcp

# Stop services
docker-compose down
```

## Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `GOOGLE_CLIENT_ID` | OAuth2 client ID | Yes |
| `GOOGLE_CLIENT_SECRET` | OAuth2 client secret | Yes |
| `OAUTH_REDIRECT_URI` | OAuth2 redirect URI | Yes |
| `SERVICE_ACCOUNT_JSON_PATH` | Path to service account JSON | No |
| `IMPERSONATE_USER_EMAIL` | Email for domain-wide delegation | No |
| `ENCRYPTION_KEY` | 32+ character encryption key | Yes |
| `JWT_SECRET` | JWT signing secret | Yes |
| `ALLOWED_DOMAINS` | Comma-separated allowed domains | No |
| `DEFAULT_SCOPES` | Default OAuth scopes | No |

### Google Scopes

| Scope | Description | Tools |
|-------|-------------|-------|
| `https://mail.google.com/` | Full Gmail access | gmail.* |
| `https://www.googleapis.com/auth/drive` | Full Drive access | drive.* |
| `https://www.googleapis.com/auth/documents` | Full Docs access | docs.* |
| `https://www.googleapis.com/auth/spreadsheets` | Full Sheets access | sheets.* |
| `https://www.googleapis.com/auth/presentations` | Full Slides access | slides.* |
| `https://www.googleapis.com/auth/calendar` | Full Calendar access | calendar.* |
| `https://www.googleapis.com/auth/contacts` | Full Contacts access | contacts.* |
| `https://www.googleapis.com/auth/tasks` | Full Tasks access | tasks.* |
| `https://www.googleapis.com/auth/keep` | Full Keep access | keep.* |
| `https://www.googleapis.com/auth/forms.body` | Full Forms access | forms.* |

### RBAC Configuration

Create `config/roles.json` to customize role permissions:

```json
{
  "admin": {
    "allowedTools": ["*"],
    "allowedScopes": ["https://mail.google.com/", "..."],
    "rateLimits": {
      "requestsPerMinute": 1000,
      "requestsPerHour": 10000,
      "requestsPerDay": 100000
    },
    "restrictions": {
      "readOnly": false,
      "maxFileSize": 104857600
    }
  },
  "editor": {
    "allowedTools": ["gmail.*", "drive.*", "docs.*", "sheets.*"],
    "rateLimits": {
      "requestsPerMinute": 500,
      "requestsPerHour": 5000,
      "requestsPerDay": 50000
    }
  },
  "viewer": {
    "allowedTools": ["*.search", "*.read", "*.list"],
    "rateLimits": {
      "requestsPerMinute": 100,
      "requestsPerHour": 1000,
      "requestsPerDay": 10000
    },
    "restrictions": {
      "readOnly": true
    }
  }
}
```

## API Usage

### Authentication

1. **OAuth Flow**
   ```bash
   # Initiate OAuth
   curl http://localhost:3000/auth/google
   
   # After callback, use the returned token
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        http://localhost:3000/tools/gmail/search \
        -d '{"query": "is:unread"}'
   ```

2. **Service Account** (automatic with domain-wide delegation)

### Tool Execution

```bash
# Send email
curl -X POST http://localhost:3000/tools/gmail/send_email \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "to": ["<EMAIL>"],
    "subject": "Hello from MCP",
    "text": "This is a test email"
  }'

# Search Gmail
curl -X POST http://localhost:3000/tools/gmail/search \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "from:<EMAIL>",
    "maxResults": 10
  }'

# Create Google Doc
curl -X POST http://localhost:3000/tools/docs/create \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Meeting Notes",
    "folderId": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
  }'
```

## Available Tools

### Gmail Tools (`gmail.*`)
- `send_email` - Send emails with attachments and threading
- `search` - Search messages with Gmail query syntax
- `get_message` - Retrieve specific message details
- `modify_labels` - Add/remove labels from messages
- `list_threads` - List conversation threads
- `watch_start` - Start push notifications
- `watch_stop` - Stop push notifications

### Drive Tools (`drive.*`) - Coming Soon
- `create_folder` - Create new folders
- `upload` - Upload files with metadata
- `download` - Download files by ID
- `search` - Search files and folders
- `move` - Move files between folders
- `share` - Share files with permissions
- `permissions_list` - List file permissions
- `trash` / `delete` - Remove files

### Cross-App Workflows (`ops.*`) - Coming Soon
- `meeting_with_agenda` - Create meeting with doc and calendar event
- `form_to_sheet_daily_summary` - Automated form response processing
- `weekly_status_pack` - Generate status reports across apps

## Monitoring & Observability

### Health Check
```bash
curl http://localhost:3000/health
```

### Metrics
- Request/response logging with correlation IDs
- Tool execution performance metrics
- Rate limiting statistics
- Database health and statistics
- Audit trail for all operations

### Grafana Dashboards
Access Grafana at `http://localhost:3001` (admin/admin) for:
- Request volume and latency
- Error rates by tool
- User activity patterns
- Resource usage trends

## Development

### Project Structure
```
src/
├── auth/           # Authentication & authorization
├── db/             # Database layer & repositories
├── mcp/            # MCP protocol implementation
├── tools/          # Tool implementations by namespace
├── utils/          # Utilities & configuration
├── server.ts       # Fastify server setup
└── index.ts        # Application entry point
```

### Adding New Tools

1. Create service class in `src/tools/{namespace}/service.ts`
2. Define tools in `src/tools/{namespace}/tools.ts`
3. Register in `src/tools/index.ts`
4. Add tests in `src/test/tools/{namespace}/`

### Testing

```bash
# Run all tests
pnpm test

# Run with coverage
pnpm test:coverage

# Run specific test file
pnpm test src/test/tool-registry.test.ts
```

## Security

- **Encryption**: All sensitive data encrypted at rest (AES-GCM)
- **RBAC**: Role-based access control with configurable permissions
- **Rate Limiting**: Per-user, per-tool rate limiting with backoff
- **Input Validation**: Zod schema validation for all inputs
- **Audit Logging**: Complete audit trail of all operations
- **Secure Headers**: Helmet.js security headers in production

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with tests
4. Run `pnpm lint` and `pnpm test`
5. Submit a pull request

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Support

- **Documentation**: [Wiki](https://github.com/your-org/google-workspace-mcp/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-org/google-workspace-mcp/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/google-workspace-mcp/discussions)
