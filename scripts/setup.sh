#!/bin/bash

# Google Workspace MCP Server Setup Script
# This script helps set up the development environment

set -e

echo "🚀 Google Workspace MCP Server Setup"
echo "===================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node --version)"
    exit 1
fi

echo "✅ Node.js $(node --version) detected"

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null; then
    echo "📦 Installing pnpm..."
    npm install -g pnpm
fi

echo "✅ pnpm $(pnpm --version) detected"

# Install dependencies
echo "📦 Installing dependencies..."
pnpm install

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p data
mkdir -p logs
mkdir -p config

# Copy configuration templates
echo "⚙️  Setting up configuration..."

if [ ! -f .env ]; then
    cp .env.sample .env
    echo "✅ Created .env file from template"
    echo "⚠️  Please edit .env with your Google Cloud credentials"
else
    echo "ℹ️  .env file already exists"
fi

if [ ! -f config/roles.json ]; then
    cp config/roles.json.sample config/roles.json
    echo "✅ Created config/roles.json from template"
else
    echo "ℹ️  config/roles.json already exists"
fi

# Generate encryption key if not set
if ! grep -q "ENCRYPTION_KEY=" .env || grep -q "ENCRYPTION_KEY=$" .env; then
    ENCRYPTION_KEY=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
    sed -i.bak "s/ENCRYPTION_KEY=.*/ENCRYPTION_KEY=$ENCRYPTION_KEY/" .env
    echo "✅ Generated encryption key"
fi

# Generate JWT secret if not set
if ! grep -q "JWT_SECRET=" .env || grep -q "JWT_SECRET=$" .env; then
    JWT_SECRET=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
    sed -i.bak "s/JWT_SECRET=.*/JWT_SECRET=$JWT_SECRET/" .env
    echo "✅ Generated JWT secret"
fi

# Build the project
echo "🔨 Building project..."
pnpm build

# Run tests
echo "🧪 Running tests..."
pnpm test

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "Next steps:"
echo "1. Edit .env with your Google Cloud credentials:"
echo "   - GOOGLE_CLIENT_ID"
echo "   - GOOGLE_CLIENT_SECRET"
echo "   - OAUTH_REDIRECT_URI"
echo ""
echo "2. (Optional) Set up Service Account for domain-wide delegation:"
echo "   - Place service-account.json in the project root"
echo "   - Set SERVICE_ACCOUNT_JSON_PATH=./service-account.json"
echo "   - Set IMPERSONATE_USER_EMAIL=<EMAIL>"
echo ""
echo "3. Start the development server:"
echo "   pnpm dev"
echo ""
echo "4. Visit http://localhost:3000/health to verify the server is running"
echo ""
echo "5. Authenticate by visiting http://localhost:3000/auth/google"
echo ""
echo "📚 For more information, see README.md"
